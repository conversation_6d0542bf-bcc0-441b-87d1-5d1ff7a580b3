{"metadata": {"description": "TLRS因子挖掘专家因子库", "version": "1.0", "creation_date": "2025-01-09", "total_factors": 30, "source": "经典量化研究与Alpha101因子库"}, "expert_factors": [{"id": 1, "name": "经典动量因子", "formula": "ts_rank(close / delay(close, 20), 20)", "description": "过去20日价格动量，基于收盘价相对强度", "category": "momentum", "complexity": 3, "effectiveness_score": 0.85}, {"id": 2, "name": "成交量价格趋势", "formula": "correlation(close, volume, 10)", "description": "价格与成交量的10日相关性，衡量量价配合度", "category": "volume_price", "complexity": 4, "effectiveness_score": 0.78}, {"id": 3, "name": "波动率调整收益", "formula": "mean(returns, 20) / std(returns, 20)", "description": "20日平均收益率除以标准差，类似夏普比率", "category": "risk_adjusted", "complexity": 4, "effectiveness_score": 0.82}, {"id": 4, "name": "相对强度指标", "formula": "close / ts_mean(close, 20) - 1", "description": "当前价格相对20日均价的偏离度", "category": "momentum", "complexity": 3, "effectiveness_score": 0.75}, {"id": 5, "name": "成交量异常因子", "formula": "volume / ts_mean(volume, 20) - 1", "description": "当前成交量相对20日平均成交量的异常程度", "category": "volume", "complexity": 3, "effectiveness_score": 0.68}, {"id": 6, "name": "高低价位置指标", "formula": "(close - low) / (high - low)", "description": "收盘价在当日高低价区间中的位置", "category": "intraday", "complexity": 2, "effectiveness_score": 0.72}, {"id": 7, "name": "价格加速度因子", "formula": "delta(delta(close, 1), 1)", "description": "价格变化的加速度，二阶差分", "category": "momentum", "complexity": 3, "effectiveness_score": 0.65}, {"id": 8, "name": "反转信号", "formula": "-1 * returns_5d", "description": "5日收益率的反转信号", "category": "reversal", "complexity": 2, "effectiveness_score": 0.58}, {"id": 9, "name": "波动率比较因子", "formula": "std(returns, 5) / std(returns, 20)", "description": "短期波动率与长期波动率的比较", "category": "volatility", "complexity": 4, "effectiveness_score": 0.7}, {"id": 10, "name": "量价分歧指标", "formula": "sign(returns) * sign(volume - ts_mean(volume, 10))", "description": "价格方向与成交量异常的一致性", "category": "volume_price", "complexity": 4, "effectiveness_score": 0.63}, {"id": 11, "name": "趋势强度指标", "formula": "ts_max(high, 20) - ts_min(low, 20)) / ts_mean(close, 20)", "description": "20日价格波动幅度相对均价的比例", "category": "trend", "complexity": 4, "effectiveness_score": 0.77}, {"id": 12, "name": "成交金额强度", "formula": "log(amount + 1) - ts_mean(log(amount + 1), 10)", "description": "成交金额的对数值相对10日均值的偏离", "category": "volume", "complexity": 3, "effectiveness_score": 0.61}, {"id": 13, "name": "价格稳定性指标", "formula": "1 / (std(close, 10) + 0.01)", "description": "价格稳定性的倒数，稳定性越高值越大", "category": "volatility", "complexity": 3, "effectiveness_score": 0.59}, {"id": 14, "name": "相对成交量指标", "formula": "volume / (ts_mean(volume, 30) + 1)", "description": "当前成交量相对30日平均的比率", "category": "volume", "complexity": 2, "effectiveness_score": 0.64}, {"id": 15, "name": "价格动量强度", "formula": "(close - delay(close, 10)) / delay(close, 10)", "description": "10日价格动量强度", "category": "momentum", "complexity": 3, "effectiveness_score": 0.79}, {"id": 16, "name": "波动率突破指标", "formula": "std(returns, 5) > ts_mean(std(returns, 5), 20)", "description": "短期波动率是否突破长期均值", "category": "volatility", "complexity": 5, "effectiveness_score": 0.66}, {"id": 17, "name": "价格区间指标", "formula": "(high + low) / 2 / close - 1", "description": "日内中间价相对收盘价的偏离", "category": "intraday", "complexity": 3, "effectiveness_score": 0.57}, {"id": 18, "name": "成交量趋势指标", "formula": "correlation(volume, sequence(20), 20)", "description": "成交量的20日线性趋势强度", "category": "volume", "complexity": 4, "effectiveness_score": 0.62}, {"id": 19, "name": "收益率偏度指标", "formula": "skewness(returns, 30)", "description": "30日收益率分布的偏度", "category": "distribution", "complexity": 4, "effectiveness_score": 0.69}, {"id": 20, "name": "价格相对位置", "formula": "(close - ts_min(low, 20)) / (ts_max(high, 20) - ts_min(low, 20))", "description": "当前价格在20日价格区间中的相对位置", "category": "momentum", "complexity": 4, "effectiveness_score": 0.74}, {"id": 21, "name": "成交量加权价格", "formula": "sum(close * volume, 10) / sum(volume, 10)", "description": "10日成交量加权平均价格", "category": "volume_price", "complexity": 4, "effectiveness_score": 0.71}, {"id": 22, "name": "价格变化率指标", "formula": "delta(log(close), 1)", "description": "对数价格的一阶差分", "category": "momentum", "complexity": 2, "effectiveness_score": 0.68}, {"id": 23, "name": "振幅指标", "formula": "(high - low) / open", "description": "日内振幅相对开盘价的比例", "category": "intraday", "complexity": 2, "effectiveness_score": 0.56}, {"id": 24, "name": "成交量波动指标", "formula": "std(volume, 10) / ts_mean(volume, 10)", "description": "成交量的变异系数", "category": "volume", "complexity": 3, "effectiveness_score": 0.6}, {"id": 25, "name": "价格突破信号", "formula": "(close > ts_max(high[1:], 19)) ? 1 : ((close < ts_min(low[1:], 19)) ? -1 : 0)", "description": "价格突破19日历史高低点的信号", "category": "breakout", "complexity": 5, "effectiveness_score": 0.73}, {"id": 26, "name": "收益率序列相关", "formula": "correlation(returns, delay(returns, 1), 20)", "description": "收益率的一阶自相关性", "category": "momentum", "complexity": 4, "effectiveness_score": 0.64}, {"id": 27, "name": "相对强弱比较", "formula": "ts_rank(close, 30) / 30", "description": "价格在30日内的相对排名", "category": "momentum", "complexity": 3, "effectiveness_score": 0.76}, {"id": 28, "name": "成交量集中度", "formula": "ts_max(volume, 20) / ts_mean(volume, 20)", "description": "20日内最大成交量与平均成交量的比值", "category": "volume", "complexity": 3, "effectiveness_score": 0.58}, {"id": 29, "name": "价格回归因子", "formula": "(close - linear_regression(close, 20)) / std(close, 20)", "description": "价格相对线性回归趋势的标准化偏离", "category": "mean_reversion", "complexity": 5, "effectiveness_score": 0.81}, {"id": 30, "name": "综合动量指标", "formula": "(close / delay(close, 5) - 1) * volume / ts_mean(volume, 20)", "description": "量价结合的综合动量指标", "category": "volume_price", "complexity": 4, "effectiveness_score": 0.83}], "factor_operations": {"basic_operations": ["add", "subtract", "multiply", "divide", "log", "exp", "sqrt", "abs", "sign", "max", "min"], "time_series_operations": ["ts_mean", "ts_sum", "ts_std", "ts_max", "ts_min", "ts_rank", "delay", "delta", "correlation", "covariance", "skewness", "kurtosis"], "conditional_operations": ["if_then_else", "greater_than", "less_than", "equal", "and", "or", "not"], "window_sizes": [3, 5, 10, 15, 20, 30, 60]}, "usage_instructions": {"description": "这个专家因子库包含了30个经过验证的量化因子公式", "factors_source": "基于Alpha101、经典技术分析和学术研究", "complexity_scale": "1-5分，5分为最复杂", "effectiveness_scale": "0-1分，1分为最有效", "recommended_usage": "在TLRS算法中作为专家轨迹的构建基础"}}