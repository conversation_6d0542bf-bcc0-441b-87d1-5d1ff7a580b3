# 核心数据处理库
pandas>=1.3.0
numpy>=1.21.0
scipy>=1.7.0

# 机器学习库
scikit-learn>=1.0.0

# 数据获取
tushare>=1.2.89

# 可视化
matplotlib>=3.5.0
seaborn>=0.11.0

# 进度条和并行处理
tqdm>=4.62.0
joblib>=1.1.0

# 技术分析库
TA-Lib>=0.4.25
# 如果安装TA-Lib有问题，可以使用：
# talib-binary>=0.4.19

# 量化投资框架（可选，用于完整回测）
# qlib>=0.9.0
# 注意：qlib安装可能比较复杂，如果只使用简单评估可以不安装

# 其他工具库
datetime
warnings
os
sys
argparse
typing
collections
pickle
json
random
multiprocessing

# 如果需要更多技术指标，可以添加：
# pandas-ta>=0.3.14b
# stockstats>=0.5.2
