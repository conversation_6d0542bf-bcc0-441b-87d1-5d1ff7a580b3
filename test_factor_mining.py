# coding: utf-8
"""
测试因子挖掘的修复版本
"""

import pandas as pd
import numpy as np
import os
import warnings
import random
from typing import List, Dict
from scipy.stats import pearsonr, spearmanr

warnings.filterwarnings('ignore')

# 简化的测试参数
MAX_SEQUENCE_LENGTH = 8
MIN_SEQUENCE_LENGTH = 2
POPULATION_SIZE = 20
GENERATIONS = 5
MUTATION_RATE = 0.2
CROSSOVER_RATE = 0.8
ELITE_RATIO = 0.1

class SimpleTLRSFactorMiner:
    """简化的TLRS因子挖掘器用于测试"""
    
    def __init__(self, factors_data: pd.DataFrame):
        self.factors_data = factors_data.copy()
        self._preprocess_data()
        self._init_operators_and_operands()
        
    def _preprocess_data(self):
        """数据预处理"""
        print("开始数据预处理...")
        
        # 移除非数值列
        numeric_cols = self.factors_data.select_dtypes(include=[np.number]).columns
        non_factor_cols = ['ts_code', 'trade_date', 'target_return_close', 'target_return_close_label', 'industry']
        self.factor_columns = [col for col in numeric_cols if col not in non_factor_cols]
        
        # 处理缺失值
        for col in self.factor_columns:
            self.factors_data[col] = self.factors_data[col].fillna(0)
        
        print(f"预处理完成，共有 {len(self.factor_columns)} 个因子")
    
    def _init_operators_and_operands(self):
        """初始化操作符和操作数"""
        self.binary_operators = ['+', '-', '*', '/']
        self.unary_operators = ['abs', 'rank', 'ts_mean', 'delta']
        self.operands = self.factor_columns[:10]  # 只使用前10个因子进行测试
        self.constants = ['1.0', '2.0']
        
        self.all_tokens = (
            self.binary_operators + 
            self.unary_operators + 
            self.operands + 
            self.constants
        )
        
        print(f"初始化完成：{len(self.operands)}个操作数")
    
    def _generate_random_sequence(self) -> List[str]:
        """生成随机序列"""
        length = random.randint(MIN_SEQUENCE_LENGTH, MAX_SEQUENCE_LENGTH)
        sequence = []
        
        for i in range(length):
            if i == 0 or random.random() < 0.6:
                # 选择操作数或常数
                if random.random() < 0.8:
                    token = random.choice(self.operands)
                else:
                    token = random.choice(self.constants)
            else:
                # 选择操作符
                if random.random() < 0.7:
                    token = random.choice(self.unary_operators)
                else:
                    token = random.choice(self.binary_operators)
            
            sequence.append(token)
        
        return sequence
    
    def _evaluate_sequence(self, sequence: List[str]) -> Dict[str, float]:
        """评估序列性能"""
        try:
            # 计算因子值
            factor_values = self._compute_factor_values(sequence)
            
            if factor_values is None or len(factor_values) == 0:
                return {
                    'ic': 0.0,
                    'rank_ic': 0.0,
                    'fitness': 0.0,
                    'complexity': len(sequence),
                    'stability': 0.0
                }
            
            # 计算IC和Rank IC  
            target = self.factors_data['target_return_close'].values
            valid_mask = ~(np.isnan(factor_values) | np.isnan(target))
            
            if valid_mask.sum() < 100:
                return {
                    'ic': 0.0,
                    'rank_ic': 0.0,
                    'fitness': 0.0,
                    'complexity': len(sequence),
                    'stability': 0.0
                }
            
            factor_valid = factor_values[valid_mask]
            target_valid = target[valid_mask]
            
            # IC (Pearson相关系数)
            ic, _ = pearsonr(factor_valid, target_valid)
            ic = abs(ic) if not np.isnan(ic) else 0.0
            
            # Rank IC (Spearman相关系数)
            rank_ic, _ = spearmanr(factor_valid, target_valid)
            rank_ic = abs(rank_ic) if not np.isnan(rank_ic) else 0.0
            
            # 综合适应度
            fitness = 0.6 * ic + 0.4 * rank_ic - 0.01 * len(sequence)
            
            return {
                'ic': ic,
                'rank_ic': rank_ic,
                'fitness': fitness,
                'complexity': len(sequence),
                'stability': 0.8  # 简化
            }
            
        except Exception as e:
            return {
                'ic': 0.0,
                'rank_ic': 0.0,
                'fitness': 0.0,
                'complexity': len(sequence),
                'stability': 0.0
            }
    
    def _compute_factor_values(self, sequence: List[str]) -> np.ndarray:
        """计算因子值"""
        try:
            if not sequence:
                return None
            
            # 使用栈来计算表达式
            stack = []
            
            for token in sequence:
                if token in self.operands:
                    values = self.factors_data[token].values
                    stack.append(values)
                elif token in self.constants:
                    const_val = float(token)
                    values = np.full(len(self.factors_data), const_val)
                    stack.append(values)
                elif token in self.unary_operators:
                    if len(stack) < 1:
                        return None
                    operand = stack.pop()
                    result = self._apply_unary_operator(token, operand)
                    if result is not None:
                        stack.append(result)
                    else:
                        return None
                elif token in self.binary_operators:
                    if len(stack) < 2:
                        return None
                    operand2 = stack.pop()
                    operand1 = stack.pop()
                    result = self._apply_binary_operator(token, operand1, operand2)
                    if result is not None:
                        stack.append(result)
                    else:
                        return None
            
            if len(stack) == 1:
                result = stack[0]
                result = np.where(np.isinf(result), 0, result)
                result = np.where(np.isnan(result), 0, result)
                return result
            else:
                return None
                
        except Exception:
            return None
    
    def _apply_unary_operator(self, op: str, operand: np.ndarray) -> np.ndarray:
        """应用一元操作符"""
        try:
            if op == 'abs':
                return np.abs(operand)
            elif op == 'rank':
                return pd.Series(operand).rank(pct=True).values
            elif op == 'ts_mean':
                return pd.Series(operand).rolling(20, min_periods=1).mean().values
            elif op == 'delta':
                return pd.Series(operand).diff().fillna(0).values
            else:
                return None
        except:
            return None
    
    def _apply_binary_operator(self, op: str, operand1: np.ndarray, operand2: np.ndarray) -> np.ndarray:
        """应用二元操作符"""
        try:
            if op == '+':
                return operand1 + operand2
            elif op == '-':
                return operand1 - operand2
            elif op == '*':
                return operand1 * operand2
            elif op == '/':
                return np.divide(operand1, operand2 + 1e-8)
            else:
                return None
        except:
            return None
    
    def _sequence_to_formula(self, sequence: List[str]) -> str:
        """将序列转换为可读的数学公式"""
        if not sequence:
            return "Empty"
        
        try:
            stack = []
            
            for token in sequence:
                if token in self.operands or token in self.constants:
                    stack.append(token)
                elif token in self.unary_operators:
                    if len(stack) < 1:
                        return f"Invalid_Unary_{token}"
                    operand = stack.pop()
                    if token == 'abs':
                        formula = f"abs({operand})"
                    elif token == 'rank':
                        formula = f"rank({operand})"
                    elif token == 'ts_mean':
                        formula = f"ts_mean({operand}, 20)"
                    elif token == 'delta':
                        formula = f"delta({operand})"
                    else:
                        formula = f"{token}({operand})"
                    stack.append(formula)
                elif token in self.binary_operators:
                    if len(stack) < 2:
                        return f"Invalid_Binary_{token}"
                    operand2 = stack.pop()
                    operand1 = stack.pop()
                    if token == '+':
                        formula = f"({operand1} + {operand2})"
                    elif token == '-':
                        formula = f"({operand1} - {operand2})"
                    elif token == '*':
                        formula = f"({operand1} * {operand2})"
                    elif token == '/':
                        formula = f"({operand1} / {operand2})"
                    else:
                        formula = f"{token}({operand1}, {operand2})"
                    stack.append(formula)
            
            if len(stack) == 1:
                return stack[0]
            else:
                return f"Invalid_Stack_{len(stack)}"
                
        except Exception as e:
            return f"Error_{str(e)}"
    
    def mine_factors(self, n_factors: int = 5) -> List[Dict]:
        """挖掘因子"""
        print(f"开始挖掘 {n_factors} 个因子...")
        
        # 初始化种群
        population = []
        for _ in range(POPULATION_SIZE):
            sequence = self._generate_random_sequence()
            evaluation = self._evaluate_sequence(sequence)
            population.append({
                'sequence': sequence,
                'evaluation': evaluation
            })
        
        # 进化过程
        for generation in range(GENERATIONS):
            print(f"第 {generation + 1}/{GENERATIONS} 代进化...")
            
            # 评估种群
            for individual in population:
                if 'evaluation' not in individual:
                    individual['evaluation'] = self._evaluate_sequence(individual['sequence'])
            
            # 按适应度排序
            population.sort(key=lambda x: x['evaluation']['fitness'], reverse=True)
            
            best_fitness = population[0]['evaluation']['fitness']
            best_ic = population[0]['evaluation']['ic']
            best_rank_ic = population[0]['evaluation']['rank_ic']
            
            print(f"  最佳适应度: {best_fitness:.4f}, IC: {best_ic:.4f}, Rank IC: {best_rank_ic:.4f}")
            print(f"  最佳序列: {population[0]['sequence']}")
            
            # 保留精英
            elite_size = int(POPULATION_SIZE * ELITE_RATIO)
            new_population = population[:elite_size]
            
            # 生成新个体
            while len(new_population) < POPULATION_SIZE:
                # 随机选择父代
                parent1 = random.choice(population[:POPULATION_SIZE//2])
                parent2 = random.choice(population[:POPULATION_SIZE//2])
                
                # 简单交叉
                if random.random() < CROSSOVER_RATE and len(parent1['sequence']) > 1 and len(parent2['sequence']) > 1:
                    mid1 = len(parent1['sequence']) // 2
                    mid2 = len(parent2['sequence']) // 2
                    child_seq = parent1['sequence'][:mid1] + parent2['sequence'][mid2:]
                else:
                    child_seq = parent1['sequence'].copy()
                
                # 变异
                if random.random() < MUTATION_RATE and child_seq:
                    idx = random.randint(0, len(child_seq) - 1)
                    child_seq[idx] = random.choice(self.all_tokens)
                
                new_population.append({'sequence': child_seq})
            
            population = new_population[:POPULATION_SIZE]
        
        # 最终评估
        for individual in population:
            individual['evaluation'] = self._evaluate_sequence(individual['sequence'])
        
        population.sort(key=lambda x: x['evaluation']['fitness'], reverse=True)
        
        return population[:n_factors]

def test_mining():
    """测试因子挖掘"""
    print("=== 测试TLRS因子挖掘系统 ===")
    
    # 检查数据文件
    factors_file = 'stock_factors_cyb.csv'
    if not os.path.exists(factors_file):
        print(f"错误：找不到因子数据文件 {factors_file}")
        return
    
    # 加载数据
    print("加载因子数据...")
    factors_data = pd.read_csv(factors_file, encoding='utf-8')
    factors_data['trade_date'] = pd.to_datetime(factors_data['trade_date'])
    
    # 只使用前1000行数据进行快速测试
    factors_data = factors_data.head(1000)
    print(f"加载完成，测试数据量: {len(factors_data)} 行")
    
    # 初始化挖掘器
    miner = SimpleTLRSFactorMiner(factors_data)
    
    # 挖掘因子
    mined_factors = miner.mine_factors(n_factors=5)
    
    # 打印结果
    print("\n=== 挖掘结果 ===")
    for i, factor in enumerate(mined_factors):
        eval_info = factor['evaluation']
        formula = miner._sequence_to_formula(factor['sequence'])
        
        print(f"\n因子 {i+1}:")
        print(f"  序列: {factor['sequence']}")
        print(f"  公式: {formula}")
        print(f"  IC: {eval_info['ic']:.4f}")
        print(f"  Rank IC: {eval_info['rank_ic']:.4f}")
        print(f"  适应度: {eval_info['fitness']:.4f}")
        print(f"  复杂度: {eval_info['complexity']}")

if __name__ == "__main__":
    test_mining()
