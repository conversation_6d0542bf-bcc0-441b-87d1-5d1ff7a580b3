# 创业板因子挖掘项目

基于TLRS（轨迹级奖励塑形）技术的专业金融因子挖掘系统，参考论文《Learning from Expert Factors: Trajectory-level Reward Shaping for Formulaic Alpha Mining》实现。

## 项目概述

本项目是一个完整的金融工程因子挖掘系统，包含以下核心功能：

1. **数据下载**：使用tushare下载创业板股票数据
2. **因子计算**：计算100+个技术分析、基本面、Alpha101等因子
3. **因子挖掘**：基于TLRS技术挖掘高质量的复合因子
4. **回测评估**：使用qlib框架进行专业的因子回测和性能评估

## 项目结构

```
factor/
├── README.md                          # 项目说明文档
├── 2507.20263v1.pdf                   # 参考论文
├── download_cyb_new.py                 # 数据下载脚本（已存在）
├── calculate_stock_factors_trade.py    # 因子计算模块
├── factor_mining_tlrs.py              # TLRS因子挖掘模块
├── factor_backtesting_qlib.py          # 回测评估模块
├── main_factor_pipeline.py            # 主控制脚本
└── requirements.txt                    # 依赖包列表
```

## 环境要求

### Python版本
- Python 3.8+

### 核心依赖包
```bash
pip install pandas numpy scipy scikit-learn
pip install tushare matplotlib seaborn tqdm joblib
pip install talib-binary  # 技术分析库
pip install qlib          # 量化投资库（可选，用于完整回测）
```

### 完整依赖安装
```bash
pip install -r requirements.txt
```

## 快速开始

### 1. 数据准备
首先运行数据下载脚本获取创业板数据：
```bash
python download_cyb_new.py
```

确保生成以下数据文件：
- `stock_basic_cyb.csv` - 股票基本信息
- `stock_daily_cyb.csv` - 日线行情数据
- `daily_basic_cyb.csv` - 每日基本面数据
- `index_daily_cyb.csv` - 指数数据

### 2. 运行完整流水线
使用主控制脚本运行完整的因子挖掘流水线：
```bash
python main_factor_pipeline.py
```

### 3. 分步执行
也可以分步执行各个阶段：

#### 步骤1：计算因子
```bash
python main_factor_pipeline.py --step 1
```
或直接运行：
```bash
python calculate_stock_factors_trade.py
```

#### 步骤2：挖掘因子
```bash
python main_factor_pipeline.py --step 2 --n-factors 10
```
或直接运行：
```bash
python factor_mining_tlrs.py
```

#### 步骤3：评估因子
```bash
python main_factor_pipeline.py --step 3 --evaluation simple
```
或直接运行：
```bash
python factor_backtesting_qlib.py
```

## 详细功能说明

### 1. 因子计算模块 (`calculate_stock_factors_trade.py`)

计算多种类型的量化因子：

#### 技术分析因子
- **动量类因子**：价格动量、累积收益率、RSI、涨跌比等
- **价格形态因子**：振幅、上下影线、实体大小、收盘价相对位置等
- **成交量因子**：相对成交量、价量协调性、OBV能量潮等
- **波动率因子**：收益率标准差、ATR、上下行波动率等
- **反转因子**：短期反转、成交量加权反转等
- **趋势因子**：移动平均偏离度、布林带位置、趋势强度等

#### 基本面因子
- **估值指标**：PE、PB及其倒数和分位数
- **市值因子**：对数市值、市值分位数
- **流动性指标**：换手率、量比等

#### 市场相关因子
- **Beta系数**：不同窗口期的Beta值
- **Alpha收益**：超额收益指标
- **市场相关性**：与市场指数的相关性

#### Alpha101因子
- 实现经典的Alpha101因子库中的部分因子

**使用示例：**
```python
from calculate_stock_factors_trade import calculate_factors_for_stock

# 计算单个股票的因子
stock_data = pd.read_csv('stock_data.csv')
factors = calculate_factors_for_stock(stock_data)
```

### 2. TLRS因子挖掘模块 (`factor_mining_tlrs.py`)

基于轨迹级奖励塑形技术挖掘高质量因子：

#### 核心特性
- **专家轨迹构建**：基于经典因子构建专家知识轨迹
- **遗传算法优化**：使用进化算法搜索最优因子组合
- **多目标优化**：同时优化IC、Rank IC、稳定性等指标
- **复杂度控制**：通过复杂度惩罚避免过拟合

#### 挖掘流程
1. 数据预处理和标准化
2. 构建专家轨迹库
3. 初始化随机种群
4. 进化优化过程
5. 因子评估和选择
6. 结果保存和可视化

**使用示例：**
```python
from factor_mining_tlrs import TLRSFactorMiner

# 初始化挖掘器
miner = TLRSFactorMiner(factors_data)

# 挖掘因子
mined_factors = miner.mine_factors(n_factors=10)

# 保存结果
miner.save_mined_factors(mined_factors)
```

#### 配置参数
```python
# TLRS参数
MAX_SEQUENCE_LENGTH = 20  # 最大序列长度
POPULATION_SIZE = 100     # 种群大小
GENERATIONS = 50          # 进化代数
MUTATION_RATE = 0.1       # 变异率
CROSSOVER_RATE = 0.7      # 交叉率

# 奖励参数
IC_WEIGHT = 0.6           # IC权重
RANK_IC_WEIGHT = 0.4      # Rank IC权重
COMPLEXITY_PENALTY = 0.01 # 复杂度惩罚
STABILITY_WEIGHT = 0.2    # 稳定性权重
```

### 3. 回测评估模块 (`factor_backtesting_qlib.py`)

提供两种评估方式：

#### 简单评估（推荐）
- 计算IC和Rank IC
- 评估因子稳定性
- 生成因子排名报告
- 不依赖外部数据，快速执行

#### 完整回测（需要qlib配置）
- 基于qlib的专业回测框架
- 支持多因子组合策略
- 计算夏普比率、最大回撤等指标
- 生成详细的回测报告

**使用示例：**
```python
from factor_backtesting_qlib import create_simple_factor_evaluator

# 简单评估
results = create_simple_factor_evaluator('mined_factors_cyb.csv')

# 完整回测
from factor_backtesting_qlib import FactorBacktester
backtester = FactorBacktester('mined_factors_cyb.csv')
results = backtester.run_comprehensive_backtest()
```

### 4. 主控制脚本 (`main_factor_pipeline.py`)

提供统一的项目管理接口：

#### 命令行参数
```bash
python main_factor_pipeline.py [选项]

选项:
  --step {1,2,3,all}        执行步骤 (默认: all)
  --n-factors N             挖掘因子数量 (默认: 10)
  --evaluation {simple,full} 评估方法 (默认: simple)
  --force-recalculate       强制重新计算因子
  --force-remining          强制重新挖掘因子
```

#### 使用示例
```bash
# 运行完整流水线
python main_factor_pipeline.py

# 只计算因子
python main_factor_pipeline.py --step 1

# 挖掘20个因子
python main_factor_pipeline.py --step 2 --n-factors 20

# 使用完整回测评估
python main_factor_pipeline.py --step 3 --evaluation full

# 强制重新执行所有步骤
python main_factor_pipeline.py --force-recalculate --force-remining
```

## 输出文件说明

### 因子计算输出
- `stock_factors_cyb.csv` - 包含所有计算的基础因子

### 因子挖掘输出
- `mined_factors_cyb.csv` - 挖掘出的高质量因子
- `mined_factors_cyb_info.json` - 因子详细信息
- `tlrs_training_history.png` - 训练过程可视化

### 回测评估输出
- `backtest_results/` - 回测结果目录
  - `backtest_results_YYYYMMDD_HHMMSS.pkl` - 完整回测结果
  - `backtest_summary_YYYYMMDD_HHMMSS.json` - 回测摘要
  - `factor_performance_YYYYMMDD_HHMMSS.csv` - 因子性能报告
  - `backtest_report_YYYYMMDD_HHMMSS.png` - 可视化报告

### 项目总结
- `project_summary_YYYYMMDD_HHMMSS.json` - 项目执行总结

## 性能优化

### 并行计算
- 因子计算支持多进程并行
- 默认使用 `CPU核心数-1` 个进程
- 可通过修改 `N_JOBS` 参数调整

### 内存优化
- 增量计算支持，避免重复计算
- 数据分块处理，降低内存占用
- 异常值处理和缺失值填充

### 计算加速
- 使用向量化操作
- 缓存中间计算结果
- 优化数据类型和存储格式

## 常见问题

### Q1: 数据下载失败怎么办？
A: 检查tushare token配置，确保网络连接正常，可能需要tushare积分权限。

### Q2: 因子计算内存不足？
A: 减少并行进程数量，或者分批处理股票数据。

### Q3: qlib初始化失败？
A: qlib需要额外的数据配置，建议使用简单评估模式。

### Q4: 挖掘出的因子效果不好？
A: 调整TLRS参数，增加进化代数或种群大小，或者修改奖励权重。

## 技术原理

### TLRS核心思想
1. **专家知识融入**：将经典因子作为专家轨迹
2. **轨迹相似度奖励**：奖励与专家轨迹相似的因子序列
3. **多目标优化**：平衡预测能力、稳定性和复杂度
4. **进化搜索**：使用遗传算法搜索最优因子组合

### 因子评估指标
- **IC (Information Coefficient)**：因子与收益的线性相关性
- **Rank IC**：因子与收益的秩相关性
- **稳定性**：因子在不同时间段的表现一致性
- **夏普比率**：风险调整后的收益指标
- **最大回撤**：投资组合的最大损失

## 扩展开发

### 添加新因子
在 `calculate_stock_factors_trade.py` 中添加新的因子计算函数：

```python
def calculate_new_factors(close, volume, windows):
    """计算新因子"""
    factors = {}
    
    for w in windows:
        # 实现新因子逻辑
        factors[f'new_factor_{w}'] = your_calculation
    
    return factors
```

### 自定义挖掘策略
继承 `TLRSFactorMiner` 类并重写相关方法：

```python
class CustomFactorMiner(TLRSFactorMiner):
    def _evaluate_sequence(self, sequence):
        # 自定义评估逻辑
        return custom_evaluation
```

### 添加新的回测策略
在 `factor_backtesting_qlib.py` 中实现新的策略类。

## 许可证

本项目仅供学习和研究使用，请勿用于商业用途。

## 联系方式

如有问题或建议，请通过GitHub Issues联系。

---

**注意**：本项目基于学术研究论文实现，因子挖掘结果仅供参考，实际投资请谨慎决策。
