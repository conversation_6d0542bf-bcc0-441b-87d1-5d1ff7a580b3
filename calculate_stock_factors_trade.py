# coding: utf-8
import pandas as pd
import numpy as np
import datetime
import os
import warnings
from joblib import Parallel, delayed
import multiprocessing
from tqdm.auto import tqdm
from scipy import stats
from scipy.stats import skew, kurtosis, pearsonr
from sklearn.linear_model import LinearRegression
import talib

warnings.filterwarnings('ignore')
warnings.filterwarnings("ignore", category=RuntimeWarning)

# --- 1. 配置参数 ---
STOCK_BASIC_FILE = 'stock_basic_cyb.csv'
STOCK_DAILY_FILE = 'stock_daily_cyb.csv'
DAILY_BASIC_FILE = 'daily_basic_cyb.csv'
INDEX_DAILY_FILE = 'index_daily_cyb.csv'  # 新增：指数日线数据文件
OUTPUT_FILE = 'stock_factors_cyb.csv'

windows = [5, 10, 20, 30]
MIN_OBS_FOR_CALC =  60
N_JOBS = multiprocessing.cpu_count() - 1

# --- 2. 数据加载与预处理函数 ---
def load_index_data(index_file):
    """加载指数数据"""
    try:
        if os.path.exists(index_file):
            index_data = pd.read_csv(index_file, encoding='utf-8')
            # 尝试不同的日期格式
            try:
                # 先尝试YYYYMMDD格式
                index_data['trade_date'] = pd.to_datetime(index_data['trade_date'], format='%Y%m%d')
            except:
                # 如果失败，尝试自动解析
                index_data['trade_date'] = pd.to_datetime(index_data['trade_date'])

            index_data['pct_chg'] = pd.to_numeric(index_data['pct_chg'], errors='coerce')
            index_data = index_data.sort_values('trade_date')
            print(f"成功加载指数数据: {len(index_data)} 条记录")
            print(f"指数数据日期范围: {index_data['trade_date'].min()} 至 {index_data['trade_date'].max()}")
            return index_data
        else:
            print(f"指数数据文件不存在: {index_file}")
            return pd.DataFrame()
    except Exception as e:
        print(f"加载指数数据失败: {e}")
        return pd.DataFrame()

def load_and_preprocess_data(stock_basic_file, stock_daily_file, daily_basic_file):
    """加载所有CSV文件并进行初步预处理 - 优化版本"""
    print("开始加载CSV文件...")

    try:
        # 使用更高效的读取方式
        print("加载股票基本信息...")
        stock_basic = pd.read_csv(stock_basic_file, encoding='utf-8')

        print("加载股票日线数据...")
        daily_cols = ['ts_code', 'trade_date', 'pre_close', 'open', 'high', 'low', 'close', 'pct_chg', 'vol', 'amount']
        stock_daily = pd.read_csv(stock_daily_file, encoding='utf-8', usecols=lambda x: x in daily_cols)

        print("加载每日基本面数据...")
        basic_cols = ['ts_code', 'trade_date', 'pe', 'pb', 'turnover_rate', 'volume_ratio', 'total_mv']
        daily_basic = pd.read_csv(daily_basic_file, encoding='utf-8', usecols=lambda x: x in basic_cols)

    except FileNotFoundError as e:
        print(f"错误：找不到文件 {e.filename}。请确保所有CSV文件都在指定路径下。")
        return None, None, None, None, None
    except Exception as e:
        print(f"加载文件时出错: {e}")
        return None, None, None, None, None

    print("开始数据清洗和类型转换...")

    # --- 数据清洗和类型转换 ---
    # 股票基础信息
    print("处理股票基础信息...")
    if 'list_date' in stock_basic.columns:
        stock_basic['list_date'] = pd.to_datetime(stock_basic['list_date'], format='%Y%m%d', errors='coerce')
    if 'delist_date' in stock_basic.columns:
        stock_basic['delist_date'] = pd.to_datetime(stock_basic['delist_date'], format='%Y%m%d', errors='coerce')
        # 只保留未摘牌的股票基本信息
        stock_basic = stock_basic[stock_basic['delist_date'].isna()]

    # 选择需要的列
    basic_keep_cols = [col for col in ['ts_code', 'name', 'area', 'industry', 'list_date'] if col in stock_basic.columns]
    stock_basic = stock_basic[basic_keep_cols]
    stock_basic = stock_basic.drop_duplicates(subset=['ts_code'])

    # 股票日行情
    print("处理股票日线数据...")
    stock_daily['trade_date'] = pd.to_datetime(stock_daily['trade_date'], format='%Y%m%d')

    # 批量转换数值列
    price_cols = ['pre_close', 'open', 'high', 'low', 'close', 'vol', 'amount', 'pct_chg']
    for col in price_cols:
        if col in stock_daily.columns:
            stock_daily[col] = pd.to_numeric(stock_daily[col], errors='coerce')

    # 过滤无效数据
    stock_daily = stock_daily.dropna(subset=['ts_code', 'trade_date', 'close'])
    stock_daily = stock_daily.sort_values(by=['ts_code', 'trade_date'])

    # 每日基本面数据
    print("处理每日基本面数据...")
    if not daily_basic.empty:
        daily_basic['trade_date'] = pd.to_datetime(daily_basic['trade_date'], format='%Y%m%d')
        # 转换数值列
        numeric_cols = ['pe', 'pb', 'turnover_rate', 'volume_ratio', 'total_mv']
        for col in numeric_cols:
            if col in daily_basic.columns:
                daily_basic[col] = pd.to_numeric(daily_basic[col], errors='coerce')
        daily_basic = daily_basic.sort_values(by=['ts_code', 'trade_date'])

    # --- 合并数据 ---
    print("开始合并数据...")

    # 首先合并股票日线和基本信息
    print("合并股票日线和基本信息...")
    stock_merged = pd.merge(stock_daily, stock_basic, on='ts_code', how='left')

    # 合并每日基本面数据
    print("合并每日基本面数据...")
    if not daily_basic.empty:
        stock_merged = pd.merge(stock_merged, daily_basic, on=['ts_code', 'trade_date'], how='left', suffixes=('', '_basic'))
    else:
        # 如果没有基本面数据，添加空列
        for col in ['pe', 'pb', 'turnover_rate']:
            stock_merged[col] = np.nan

    # 不再合并财务数据

    print("最终排序...")
    stock_merged = stock_merged.sort_values(by=['ts_code', 'trade_date'])

    print(f"数据合并完成！最终数据量: {len(stock_merged)} 行")
    return stock_basic, stock_daily, daily_basic, stock_merged

def calculate_volume_price_momentum_factor(high, low, close, pre_close, volume, pct_chg, window=20):
    """
    因子1: windows日内上涨日量价动量-下跌日量价惩罚
    """
    try:
        # 计算振幅
        amplitude = (high - low) / pre_close

        # 判断上涨日和下跌日
        up_days = pct_chg > 0
        down_days = pct_chg < 0

        # 计算量价动量（成交量 * 振幅）
        volume_price_momentum = volume * amplitude

        # 分别计算上涨日和下跌日的量价动量
        up_momentum = np.where(up_days, volume_price_momentum, 0)
        down_momentum = np.where(down_days, volume_price_momentum, 0)

        # 【核心修改】在创建Series时，传入原始索引 high.index
        up_momentum_sum = pd.Series(up_momentum, index=high.index).rolling(window=window, min_periods=window//2).sum()
        down_momentum_sum = pd.Series(down_momentum, index=high.index).rolling(window=window, min_periods=window//2).sum()

        # 因子 = 上涨日量价动量 - 下跌日量价惩罚
        factor = up_momentum_sum - down_momentum_sum

        return factor.fillna(0)
    except:
        # 【建议】这里最好打印一下错误信息，以便调试
        # import traceback
        # print(traceback.format_exc())
        return pd.Series(0, index=high.index) # 返回的错误Series也应带索引

def calculate_volatility_asymmetry_factor(high, low, close, pre_close, pct_chg, window=20):
    """
    因子2: 波动率不对称因子
    """
    try:
        # 计算振幅
        amplitude = (high - low) / pre_close

        # 判断上涨日和下跌日
        up_days = pct_chg > 0
        down_days = pct_chg < 0

        # 分别计算上涨日和下跌日的振幅
        up_amplitude = np.where(up_days, amplitude, np.nan)
        down_amplitude = np.where(down_days, amplitude, np.nan)

        # 【核心修改】在创建Series时，传入原始索引 high.index
        avg_amp_up = pd.Series(up_amplitude, index=high.index).rolling(window=window, min_periods=window//4).mean()
        avg_amp_down = pd.Series(down_amplitude, index=high.index).rolling(window=window, min_periods=window//4).mean()

        # 波动率不对称因子 = 下跌日平均振幅 / 上涨日平均振幅
        factor = avg_amp_down / (avg_amp_up + 1e-8)  # 避免除零

        return factor.fillna(0)
    except:
        return pd.Series(0, index=high.index)

def calculate_shock_intensity_factor(close, volume, pct_chg, window_shock=20, window_vol=60, vol_multiplier=3, pct_threshold=5):
    """
    因子3: 冲击强度因子
    """
    try:
        # 计算过去M日平均成交量
        avg_volume = volume.rolling(window=window_vol, min_periods=window_vol//2).mean()

        # 判断冲击日条件
        volume_condition = volume > (avg_volume * vol_multiplier)
        pct_condition = np.abs(pct_chg) > pct_threshold
        shock_days = volume_condition & pct_condition

        # 计算冲击强度
        volume_ratio = volume / (avg_volume + 1e-8)
        shock_intensity = np.where(shock_days, pct_chg * volume_ratio, 0)

        # 【核心修改】在创建Series时，传入原始索引 close.index
        factor = pd.Series(shock_intensity, index=close.index).rolling(window=window_shock, min_periods=window_shock//2).sum()

        return factor.fillna(0)
    except:
        return pd.Series(0, index=close.index)

def calculate_market_downside_excess_return_factor(stock_pct_chg, index_pct_chg, window=20):
    """
    因子4: 市场下行日超额收益因子
    """
    try:
        # 判断市场下行日（指数下跌）
        market_down_days = index_pct_chg < 0

        # 计算超额收益率
        excess_return = stock_pct_chg - index_pct_chg

        # 只在市场下行日计算超额收益
        downside_excess_return = np.where(market_down_days, excess_return, np.nan)

        # 【核心修改】在创建Series时，传入原始索引 stock_pct_chg.index
        factor = pd.Series(downside_excess_return, index=stock_pct_chg.index).rolling(window=window, min_periods=window//4).mean()

        return factor.fillna(0)
    except:
        return pd.Series(0, index=stock_pct_chg.index)

# --- 新增顶级统计学因子函数 ---

# --- 组1: 高阶矩与分布形态因子 ---

def calculate_rolling_skewness(returns, window=20):
    """
    因子5: 滚动收益率偏度 (Rolling Skewness)
    逻辑: 衡量收益率分布的不对称性。正偏度（右偏）的股票可能意味着其有更多机会出现极端正收益（类似彩票效应），
          投资者可能愿意为此支付溢价，导致未来预期收益率降低。负偏度（左偏）则相反，可能预示着崩盘风险，需要更高的收益补偿。
          我们预期负偏度因子为正向alpha。
    """
    return returns.rolling(window=window, min_periods=window//2).skew().fillna(0)

def calculate_rolling_kurtosis(returns, window=20):
    """
    因子6: 滚动收益率峰度 (Rolling Kurtosis)
    逻辑: 衡量收益率分布的“尾部厚度”。高锋度意味着出现极端事件（暴涨或暴跌）的概率更高。
          投资者通常厌恶这种不确定性（尤其是左尾风险），因此高锋度的股票可能需要提供更高的风险溢价。
    """
    return returns.rolling(window=window, min_periods=window//2).kurt().fillna(0)

def calculate_downside_deviation(returns, window=20):
    """
    因子7: 下行标准差 (Downside Deviation)
    逻辑: 只计算负收益率的标准差，是比总波动率更精准的风险度量，因为它只关注投资者厌恶的“坏”波动。
          下行风险越高的股票，理论上应有越高的预期收益作为补偿。
    """
    downside_returns = returns[returns < 0]
    return downside_returns.rolling(window=window, min_periods=window//4).std().fillna(0)


# --- 组2: 风险结构与Beta因子 ---


def calculate_beta_instability(rolling_beta, window=20):
    """
    因子11: Beta不稳定性 (Beta Instability)
    逻辑: 计算滚动Beta本身的标准差。Beta越不稳定，意味着股票的风险特征越不稳定，难以预测。
          这种不确定性是一种风险，高不确定性的股票未来收益可能更低。
    """
    return rolling_beta.rolling(window=window, min_periods=window//2).std().fillna(0)

def calculate_historical_cvar_5pct(returns, window=60):
    """
    因子12: 5%历史条件风险价值 (Historical CVaR 5%)
    逻辑: 计算在最差的5%的日子里，股票的平均损失是多少。这是一个比VaR更优秀的尾部风险度量指标，
          因为它关注“一旦发生极端损失，损失有多大”。CVaR越高，尾部风险越大，预期收益应越高。
    """
    # 计算5%分位数 (VaR)
    var_5 = returns.rolling(window=window).quantile(0.05)
    # 计算CVaR
    def cvar_calc(x):
        # 找到分位数
        q5 = np.quantile(x, 0.05)
        # 计算所有低于该分位数的收益的均值
        return x[x <= q5].mean()
    cvar = returns.rolling(window=window).apply(cvar_calc, raw=True)
    return cvar.fillna(0)


# --- 组3: 流动性与交易行为因子 ---

def calculate_amihud_illiquidity_proxy(returns, amount, window=20):
    """
    因子13: Amihud非流动性指标代理 (Amihud Illiquidity Proxy)
    逻辑: 衡量单位成交金额所能引起的股价变动幅度。|Return| / Amount。值越大，流动性越差。
          流动性差的股票交易成本更高，风险更大，投资者会要求流动性溢价作为补偿。
    """
    # 成交额单位为元，返回值为百分比。为避免数值过小，放大1e6倍
    illiq = (np.abs(returns) * 100) / (amount + 1e-8) * 1e6
    return illiq.rolling(window=window, min_periods=window//2).mean().fillna(0)

def calculate_turnover_volatility(turnover_rate, window=20):
    """
    因子14: 换手率波动率 (Turnover Volatility)
    逻辑: 衡量换手率的稳定性。换手率波动巨大，可能意味着投资者对该股票的关注度时高时低，信息不对称程度高，
          或者有大量短线资金在炒作。这种不稳定性是一种风险，可能预示着较低的未来收益。
    """
    return turnover_rate.rolling(window=window, min_periods=window//2).std().fillna(0)

def calculate_volume_concentration_hhi(volume, window=20):
    """
    因子15: 成交量集中度 (Volume Concentration - HHI)
    逻辑: 使用赫芬达尔-赫希曼指数(HHI)来衡量一个窗口期内，成交量是否集中在少数几天。
          HHI = sum((daily_vol / total_window_vol)^2)。值越高，集中度越高。
          成交量高度集中可能意味着由少数几次信息事件或资金冲击驱动，稳定性差。
    """
    total_vol_in_window = volume.rolling(window=window).sum()
    # (vol / total_vol)^2
    hhi_daily = (volume / (total_vol_in_window + 1e-8))**2
    return hhi_daily.rolling(window=window, min_periods=window//2).sum().fillna(0)

def calculate_return_turnover_correlation(returns, turnover_rate, window=30):
    """
    因子16: 收益率与换手率的相关性 (Return-Turnover Correlation)
    逻辑: 量价相关性。正相关性强，意味着“放量上涨，缩量下跌”，被认为是健康的技术形态。
          负相关性强，意味着“放量下跌”，是恐慌的信号。我们预期高相关性对应着更好的未来表现。
    """
    return returns.rolling(window=window).corr(turnover_rate).fillna(0)

# --- 组4: 价格动态与微观结构因子 ---

def calculate_return_autocorrelation(returns, window=20, lag=1):
    """
    因子17: 收益率自相关性 (Return Autocorrelation)
    逻辑: 衡量今天的收益率与昨天收益率的相关性。显著为负的自相关性意味着“反转效应”，
          显著为正意味着“动量效应”。这个因子直接捕捉了这两种效应的强度。
    """
    return returns.rolling(window=window).corr(returns.shift(lag)).fillna(0)

def calculate_close_to_high_low_ratio(close, high, low, window=10):
    """
    因子18: 收盘价在日内高低价中的位置 (Close-to-High-Low Ratio)
    逻辑: (Close - Low) / (High - Low)。该值接近1，说明收盘强势；接近0，说明收盘弱势。
          这是一个衡量日内动量的指标。持续强势收盘的股票可能继续走强。我们计算其N日均值。
    """
    ratio = (close - low) / (high - low + 1e-8)
    return ratio.rolling(window=window, min_periods=window//2).mean().fillna(0)

def calculate_max_drawdown(close, window=60):
    """
    因子19: 最大回撤 (Max Drawdown)
    逻辑: 衡量在一个窗口期内，股价从最高点回落的最大幅度。这是一个经典的风险指标，直接反映了投资该股票可能面临的最大损失。
          最大回撤越大的股票，风险越高，应有更高的收益补偿。
    """
    rolling_max = close.rolling(window=window, min_periods=window//2).max()
    drawdown = (close - rolling_max) / (rolling_max + 1e-8)
    return drawdown.rolling(window=window, min_periods=window//2).min().fillna(0) # min因为回撤是负数


# --- 组5: 更多创新与组合因子 ---
def calculate_volatility_of_volatility(returns, window_vol=10, window_std=20):
    """
    因子21: 波动率的波动率 (Volatility of Volatility)
    逻辑: 先计算一个短周期的滚动波动率（如10日），然后再计算这个波动率序列的标准差。
          它衡量了股票风险水平的稳定性。高V_o_V意味着风险水平忽高忽低，不确定性强，应被视为负面信号。
    """
    rolling_vol = returns.rolling(window=window_vol).std()
    return rolling_vol.rolling(window=window_std).std().fillna(0)

def calculate_price_relative_to_moving_median(close, window=20):
    """
    因子22: 股价相对滚动中位数的位置 (Price Relative to Moving Median)
    逻辑: Close / Rolling_Median(Close)。中位数比均值对极端值更不敏感（鲁棒性更强）。
          这是一个更稳健的动量或反转指标。当价格显著高于滚动中位数时，可能存在动量；显著低于时，可能存在反转机会。
    """
    rolling_median = close.rolling(window=window, min_periods=window//2).median()
    return (close / (rolling_median + 1e-8)).fillna(1) - 1 # 标准化到0附近

def calculate_upside_downside_volume_ratio(close, pre_close, volume, window=20):
    """
    因子23: 上涨日成交量与下跌日成交量比率 (Up/Down Volume Ratio)
    逻辑: 类似于OBV（On-Balance Volume）的思路，但更直接。计算窗口期内，所有上涨日的总成交量除以所有下跌日的总成交量。
          该比率远大于1，说明买盘力量强大，资金在积极入场，是看涨信号。
    """
    up_volume = volume.where(close > pre_close, 0).rolling(window=window).sum()
    down_volume = volume.where(close < pre_close, 0).rolling(window=window).sum()
    return (up_volume / (down_volume + 1e-8)).fillna(1)

def calculate_tail_ratio(returns, window=60):
    """
    因子24: 尾部比率 (Tail Ratio)
    逻辑: 计算95分位数的收益除以5分位数收益的绝对值。 abs(quantile(0.95) / quantile(0.05))。
          这个比率衡量了上行潜力和下行风险的不对称性。比率越高，说明右尾（大涨）潜力相对于左尾（大跌）风险越大。
    """
    q95 = returns.rolling(window=window).quantile(0.95)
    q05 = returns.rolling(window=window).quantile(0.05)
    return (q95 / (np.abs(q05) + 1e-8)).fillna(1)

def calculate_pe_adjusted_momentum(pct_chg, pe, window=20):
    """
    因子25: PE调整后的动量 (PE-Adjusted Momentum)
    逻辑: 将动量（累计收益）除以市盈率PE。旨在寻找那些“估值合理的上涨”。
          避免选择那些仅仅因为被过度炒作、估值泡沫化而价格上涨的股票。我们预期这是一个更稳健的动量因子。
    """
    cumulative_return = (1 + pct_chg/100).rolling(window=window).apply(np.prod, raw=True) - 1
    # PE可能为0或负，取倒数（EP）再做处理更稳健
    ep_ratio = 1 / (pe + 1e-8)
    return (cumulative_return * ep_ratio).fillna(0)

def calculate_zero_trading_days_ratio(volume, window=20):
    """
    因子27: 零成交日占比 (Zero-Trading Days Ratio)
    逻辑: 计算窗口期内成交量为0或极低的天数占比。这是一个极端的非流动性指标。
          占比越高的股票，流动性越差，信息更新越慢，风险越高。
    """
    zero_days = (volume < 1) # 成交量小于1手视为0
    return zero_days.rolling(window=window).sum() / window

def calculate_giot_market_depth_proxy(pct_chg, amount, window=20):
    """
    因子28: 市场深度代理指标 (Market Depth Proxy)
    逻辑: (Amount / sqrt(Total Market Value)) / |pct_chg|。这是对Giot(2005)市场深度指标的简化。
          衡量在给定价格变动下，市场能够吸收的成交额。值越大，说明市场深度越好，流动性越强。
    """
    # total_mv 单位是万元， amount是元，统一单位
    market_depth = (amount / 10000) / (np.abs(pct_chg/100) + 1e-8)
    return market_depth.rolling(window=window).mean().fillna(0)

def calculate_return_gap_ratio(open_price, pre_close, window=20):
    """
    因子29: 收益缺口率 (Return Gap Ratio)
    逻辑: 计算隔夜跳空缺口 (open/pre_close - 1) 的N日均值。
          持续的正向缺口可能意味着市场情绪高涨或有持续的隔夜利好，是动量的表现。
          持续的负向缺口则相反。
    """
    gap = open_price / pre_close - 1
    return gap.rolling(window=window).mean().fillna(0)

def calculate_realized_vol_vs_intraday_range(returns, high, low, close, window=20):
    """
    因子30: 已实现波动率与日内振幅比 (Realized Vol / Intraday Range)
    逻辑: 已实现波动率(N日收益率标准差) vs 日内振幅((high-low)/close)的N日均值。
          比值高，说明收盘价之间的波动（隔夜风险）大于日内波动，可能隐藏更多信息。
          比值低，说明大部分波动都在日内完成，价格发现效率可能更高。
    """
    realized_vol = returns.rolling(window=window).std()
    intraday_range = ((high - low) / close).rolling(window=window).mean()
    return (realized_vol / (intraday_range + 1e-8)).fillna(1)

# --- 组6: 动量与反转因子 ---

def calculate_momentum_factor(returns, window=20):
    """
    因子31: 传统动量因子 (Momentum Factor)
    逻辑: 过去N日累计收益率，经典的动量信号。
    """
    return (1 + returns).rolling(window=window).apply(np.prod, raw=True).fillna(1) - 1

def calculate_reversal_factor(returns, window=5):
    """
    因子32: 短期反转因子 (Short-term Reversal)
    逻辑: 短期反转效应，过去N日收益率的负值。
    """
    return -(1 + returns).rolling(window=window).apply(np.prod, raw=True).fillna(1) + 1

def calculate_rsi_factor(close, window=14):
    """
    因子33: RSI相对强弱指标
    逻辑: 经典技术指标，衡量超买超卖状态。
    """
    delta = close.diff()
    gain = delta.where(delta > 0, 0).rolling(window=window).mean()
    loss = (-delta).where(delta < 0, 0).rolling(window=window).mean()
    rs = gain / (loss + 1e-8)
    rsi = 100 - (100 / (1 + rs))
    return (rsi - 50) / 50  # 标准化到[-1,1]

def calculate_macd_signal(close, fast=12, slow=26, signal=9):
    """
    因子34: MACD信号线
    逻辑: MACD指标的信号线，衡量趋势变化。
    """
    ema_fast = close.ewm(span=fast).mean()
    ema_slow = close.ewm(span=slow).mean()
    macd_line = ema_fast - ema_slow
    signal_line = macd_line.ewm(span=signal).mean()
    return macd_line - signal_line

# --- 组7: 波动率相关因子 ---

def calculate_garch_volatility_proxy(returns, window=20):
    """
    因子35: GARCH波动率代理指标
    逻辑: 简化的GARCH模型，考虑波动率聚类效应。
    """
    # 简化的GARCH(1,1)代理
    squared_returns = returns**2
    avg_vol = squared_returns.rolling(window=window).mean()
    vol_persistence = squared_returns.rolling(window=2).corr(squared_returns.shift(1))
    return avg_vol * (1 + vol_persistence.fillna(0))

def calculate_volatility_risk_premium(returns, window_short=5, window_long=20):
    """
    因子36: 波动率风险溢价
    逻辑: 长期波动率与短期波动率的差异，衡量波动率期限结构。
    """
    short_vol = returns.rolling(window=window_short).std()
    long_vol = returns.rolling(window=window_long).std()
    return long_vol - short_vol

def calculate_volatility_skew_factor(returns, window=30):
    """
    因子37: 波动率偏度因子
    逻辑: 计算绝对收益率的偏度，衡量波动率分布的不对称性。
    """
    abs_returns = np.abs(returns)
    return abs_returns.rolling(window=window).skew().fillna(0)

# --- 组8: 价值与成长因子 ---

def calculate_pb_momentum_factor(pb, returns, window=20):
    """
    因子38: PB动量因子
    逻辑: 将PB倒数与价格动量结合，寻找低估值的上涨股票。
    """
    if pb.isna().all():
        return pd.Series(0, index=returns.index)
    pb_inv = 1 / (pb + 1e-8)
    momentum = (1 + returns).rolling(window=window).apply(np.prod, raw=True) - 1
    return pb_inv * momentum

def calculate_earnings_quality_proxy(pe, pb):
    """
    因子39: 盈利质量代理指标
    逻辑: PE和PB的比值关系，衡量盈利质量。
    """
    if pe.isna().all() or pb.isna().all():
        return pd.Series(0, index=pe.index)
    # ROE代理 = 1/PB / (1/PE) = PE/PB
    roe_proxy = (pe + 1e-8) / (pb + 1e-8)
    return np.log(roe_proxy + 1e-8).fillna(0)

# --- 组9: 流动性深度因子 ---

def calculate_bid_ask_proxy(high, low, volume, window=10):
    """
    因子40: 买卖价差代理指标
    逻辑: 使用日内高低价差和成交量来代理买卖价差。
    """
    price_spread = (high - low) / ((high + low) / 2)
    volume_weight = 1 / (volume + 1e-8)
    spread_proxy = price_spread * volume_weight
    return spread_proxy.rolling(window=window).mean().fillna(0)

def calculate_price_impact_factor(returns, volume, window=15):
    """
    因子41: 价格冲击因子
    逻辑: 收益率与成交量的非线性关系，衡量大额交易的价格冲击。
    """
    abs_returns = np.abs(returns)
    volume_normalized = volume / volume.rolling(window=window*2).mean()
    price_impact = abs_returns / np.log(volume_normalized + 1e-8 + 1)
    return price_impact.rolling(window=window).mean().fillna(0)

# --- 组10: 趋势强度因子 ---

def calculate_trend_consistency_factor(close, window=20):
    """
    因子42: 趋势一致性因子
    逻辑: 衡量价格趋势的一致性，基于连续上涨/下跌天数。
    """
    price_change = close.diff()
    up_days = (price_change > 0).astype(int)
    down_days = (price_change < 0).astype(int)
    
    # 计算连续上涨/下跌的一致性
    up_consistency = up_days.rolling(window=window).sum() / window
    down_consistency = down_days.rolling(window=window).sum() / window
    
    # 趋势一致性 = |上涨一致性 - 下跌一致性|
    trend_consistency = np.abs(up_consistency - down_consistency)
    return trend_consistency.fillna(0)

def calculate_breakout_strength_factor(close, high, low, window=20):
    """
    因子43: 突破强度因子
    逻辑: 衡量价格突破历史高点或低点的强度。
    """
    rolling_high = high.rolling(window=window).max()
    rolling_low = low.rolling(window=window).min()
    
    # 突破强度
    upside_breakout = (close - rolling_high) / rolling_high
    downside_breakout = (rolling_low - close) / rolling_low
    
    # 综合突破强度（正值表示向上突破，负值表示向下突破）
    breakout_strength = upside_breakout - downside_breakout
    return breakout_strength.fillna(0)

# --- 组11: 信息含量因子 ---

def calculate_information_ratio_factor(returns, index_returns, window=30):
    """
    因子44: 信息比率因子
    逻辑: 超额收益相对于跟踪误差的比率，衡量主动管理价值。
    """
    if len(index_returns) != len(returns):
        return pd.Series(0, index=returns.index)
    
    excess_returns = returns - index_returns
    tracking_error = excess_returns.rolling(window=window).std()
    avg_excess_return = excess_returns.rolling(window=window).mean()
    
    info_ratio = avg_excess_return / (tracking_error + 1e-8)
    return info_ratio.fillna(0)

def calculate_alpha_decay_factor(returns, window_short=5, window_long=20):
    """
    因子45: Alpha衰减因子
    逻辑: 短期和长期Alpha的差异，衡量Alpha的持续性。
    """
    short_alpha = returns.rolling(window=window_short).mean()
    long_alpha = returns.rolling(window=window_long).mean()
    alpha_decay = short_alpha - long_alpha
    return alpha_decay.fillna(0)

# --- 组12: 机器学习启发因子 ---

def calculate_price_acceleration_factor(close, window=10):
    """
    因子46: 价格加速度因子
    逻辑: 价格变化的二阶导数，衡量趋势变化的加速度。
    """
    price_velocity = close.diff()
    price_acceleration = price_velocity.diff()
    return price_acceleration.rolling(window=window).mean().fillna(0)

def calculate_volume_acceleration_factor(volume, window=10):
    """
    因子47: 成交量加速度因子
    逻辑: 成交量变化的二阶导数，衡量交易活跃度的变化趋势。
    """
    volume_velocity = volume.diff()
    volume_acceleration = volume_velocity.diff()
    normalized_acc = volume_acceleration / (volume.rolling(window=window*2).mean() + 1e-8)
    return normalized_acc.rolling(window=window).mean().fillna(0)

def calculate_regime_change_factor(returns, volume, window=15):
    """
    因子48: 市场状态变化因子
    逻辑: 基于收益率和成交量的联合分布变化，识别市场状态转换。
    """
    # 收益率标准化
    returns_std = (returns - returns.rolling(window=window*2).mean()) / (returns.rolling(window=window*2).std() + 1e-8)
    # 成交量标准化
    volume_std = (volume - volume.rolling(window=window*2).mean()) / (volume.rolling(window=window*2).std() + 1e-8)
    
    # 联合分布指标
    joint_indicator = returns_std * volume_std
    regime_change = joint_indicator.rolling(window=window).std()
    return regime_change.fillna(0)

# --- 组13: 高频特征因子 ---

def calculate_intraday_momentum_factor(open_price, close, high, low):
    """
    因子49: 日内动量因子
    逻辑: 综合开盘、收盘、最高、最低价的日内动量信息。
    """
    # 开盘相对前收盘的跳空
    gap_return = (open_price / close.shift(1) - 1).fillna(0)
    # 日内收益率
    intraday_return = (close / open_price - 1).fillna(0)
    # 日内振幅
    intraday_range = ((high - low) / open_price).fillna(0)
    
    # 综合日内动量指标
    intraday_momentum = intraday_return * (1 + intraday_range) * (1 + np.abs(gap_return))
    return intraday_momentum.fillna(0)

def calculate_overnight_risk_factor(open_price, close, window=10):
    """
    因子50: 隔夜风险因子
    逻辑: 隔夜跳空的波动性，衡量隔夜风险。
    """
    overnight_return = (open_price / close.shift(1) - 1).fillna(0)
    overnight_volatility = overnight_return.rolling(window=window).std()
    return overnight_volatility.fillna(0)



def calculate_return(open, close):

    # 计算周回报率（百分比）
    weekly_return = (close.shift(-1) / open.shift(-1) -1)
    
    return weekly_return

def classify_target_label(target_return):
    """
    根据给定的规则，将连续的涨跌幅转换为分类标签
    :param target_return: pd.Series, 原始涨跌幅百分比
    :return: pd.Series, 分类后的标签
    """
    conditions = [
        target_return > 0.1,          # 大于10个点
        (target_return >= 0.03) & (target_return <= 0.1),  # 5到10个点
        target_return < 0.03          # 小于5个点
    ]

    choices = [
        target_return,      # 大于10个点
        0.03,    # 5到10个点
        -0.1      # 小于5个点
    ]
    
    # 使用 np.select 实现
    classified_label = np.select(conditions, choices, default=np.nan)
    
    return pd.Series(classified_label, index=target_return.index)

# --- 11. 主函数：计算所有因子 ---
def calculate_factors_for_stock(stock_data, current_date=None, index_data=None):
    """为单个ts_code的股票数据计算所有因子"""
    ts_code = stock_data['ts_code'].iloc[0]

    # 确保数据按日期排序
    stock_data = stock_data.sort_values(by='trade_date')

    # 检查数据量是否足够进行计算
    if len(stock_data) < MIN_OBS_FOR_CALC:
        # print(f"数据不足，跳过 {ts_code} (需要 {MIN_OBS_FOR_CALC} 天, 只有 {len(stock_data)} 天)")
        return None

    # 创建结果字典
    results_dict = {}

    # --- 1. 基础数据 ---
    results_dict['ts_code'] = stock_data['ts_code']
    results_dict['trade_date'] = stock_data['trade_date']
    results_dict['open'] = stock_data['open']
    results_dict['high'] = stock_data['high']
    results_dict['low'] = stock_data['low']
    results_dict['close'] = stock_data['close']
    results_dict['pct_chg'] = stock_data['pct_chg']
    results_dict['vol'] = stock_data['vol']
    results_dict['amount'] = stock_data['amount']

    if 'industry' in stock_data.columns:
        industry_value = stock_data['industry'].iloc[0]
        results_dict['industry'] = pd.Series(industry_value, index=stock_data.index)
    else:
        results_dict['industry'] = pd.Series('', index=stock_data.index)

    # --- 准备基础的Pandas Series ---
    close = stock_data['close'].astype(float)
    high = stock_data['high'].astype(float)
    low = stock_data['low'].astype(float)
    open_price = stock_data['open'].astype(float)
    volume = stock_data['vol'].astype(float)
    amount = stock_data['amount'].astype(float)
    pct_chg_series = stock_data['pct_chg'].astype(float) # 改个名字以示区分
    pre_close = stock_data['pre_close'].astype(float)
    pe = stock_data.get('pe', pd.Series(np.nan, index=stock_data.index)).astype(float).fillna(0)
    turnover_rate = stock_data.get('turnover_rate', pd.Series(np.nan, index=stock_data.index)).astype(float).fillna(0)

    # 收益率序列 (这是一个核心序列，确保是Series)
    returns = close.pct_change().fillna(0)

    # 对齐的指数收益率序列 (如果存在)，确保是Series
    index_returns = pd.Series(0.0, index=stock_data.index) # 默认值为0的Series
    if index_data is not None and not index_data.empty:
        try:
            stock_dates = pd.to_datetime(stock_data['trade_date'])
            index_data_copy = index_data.copy()
            index_data_copy['trade_date'] = pd.to_datetime(index_data_copy['trade_date'])
            index_aligned = index_data_copy.set_index('trade_date').reindex(stock_dates)
            
            # 【【【核心修正点 1】】】: 删除 .values，保持为Pandas Series
            # 将pct_chg从百分比转为小数
            index_returns = (index_aligned['pct_chg'].fillna(0) / 100)
            # 再次确认填充所有可能的NaN
            index_returns = index_returns.fillna(0)

        except Exception as e:
            print(f"对齐指数数据时出错 for {ts_code}: {e}")
            # 如果出错，确保返回的是一个有索引的0序列
            index_returns = pd.Series(0.0, index=stock_data.index)
            
    # --- 计算 Target (这里使用未来函数是正确的) --- 
    results_dict['target_return_close'] = calculate_return(open_price, close)
    results_dict['target_return_close_label'] = classify_target_label(results_dict['target_return_close'])
    #results_dict['target_return_high'] = calculate_return(open_price, high)
    #results_dict['target_return_high_label'] = classify_target_label(results_dict['target_return_high'])



    # --- 因子计算 ---
    # 因子1: 量价动量因子
    for window in windows:
        results_dict[f'VOLUME_PRICE_MOMENTUM_{window}'] = -calculate_volume_price_momentum_factor(
            high, low, close, pre_close, volume, pct_chg_series, window)
    
    # 因子2: 波动率不对称因子
    for window in windows:
        results_dict[f'VOLATILITY_ASYMMETRY_{window}'] = calculate_volatility_asymmetry_factor(
            high, low, close, pre_close, pct_chg_series, window)
    
    # 因子3: 冲击强度因子
    for window in windows:
        results_dict[f'SHOCK_INTENSITY_{window}'] = -calculate_shock_intensity_factor(
            close, volume, pct_chg_series, window, 60, 3, 5)

    # 因子4: 市场下行日超额收益因子
    # 【【【核心修正点 2】】】: 确保传入的是 pct_chg_series 和 index_returns 这两个Series
    for window in windows:
        results_dict[f'MARKET_DOWNSIDE_EXCESS_30'] = -calculate_market_downside_excess_return_factor(
            pct_chg_series, index_returns * 100, 30) # 因子函数内部可能需要百分比，传回去

    # --- 组1: 高阶矩与分布形态因子 ---
    for window in windows:
        results_dict[f'SKEWNESS_{window}'] = -calculate_rolling_skewness(returns, window)
        results_dict[f'KURTOSIS_{window}'] = calculate_rolling_kurtosis(returns, window)
        results_dict[f'DOWNSIDE_DEV_{window}'] = calculate_downside_deviation(returns, window)


    results_dict['CVAR_5PCT_60'] = calculate_historical_cvar_5pct(returns, 60)

    # --- 组3: 流动性与交易行为因子 ---
    for window in windows:
        results_dict[f'AMIHUD_ILLIQ_{window}'] = calculate_amihud_illiquidity_proxy(returns, amount, window)
        results_dict[f'TURNOVER_VOL_{window}'] = -calculate_turnover_volatility(turnover_rate, window)
        results_dict[f'VOL_CONCENTRATION_HHI_{window}'] = -calculate_volume_concentration_hhi(volume, window)
        results_dict[f'RET_TURN_CORR_{window}'] = calculate_return_turnover_correlation(returns, turnover_rate, window)
    # --- 组4: 价格动态与微观结构因子 ---
    for window in windows:
        results_dict[f'RET_AUTOCORR_LAG1_{window}'] = -calculate_return_autocorrelation(returns, window, 1)
        results_dict[f'CLOSE_TO_HILO_RATIO_{window}'] = calculate_close_to_high_low_ratio(close, high, low, window)
        results_dict[f'MAX_DRAWDOWN_{window}'] = calculate_max_drawdown(close, window)
    # 因子20: 残差收益率偏度

    # --- 组5: 更多创新与组合因子 ---
    for window in windows:
        results_dict[f'VOL_OF_VOL_10_{window}'] = -calculate_volatility_of_volatility(returns, 10, window)
        results_dict[f'PRICE_VS_MEDIAN_{window}'] = calculate_price_relative_to_moving_median(close, window)
        results_dict[f'UP_DOWN_VOL_RATIO_{window}'] = calculate_upside_downside_volume_ratio(close, pre_close, volume, window)
        results_dict[f'PE_ADJ_MOMENTUM_{window}'] = calculate_pe_adjusted_momentum(pct_chg_series, pe, window)
        results_dict[f'ZERO_TRADE_RATIO_{window}'] = -calculate_zero_trading_days_ratio(volume, window)
        results_dict[f'MARKET_DEPTH_PROXY_{window}'] = -calculate_giot_market_depth_proxy(pct_chg_series, amount, window)
        results_dict[f'GAP_RATIO_{window}'] = calculate_return_gap_ratio(open_price, pre_close, window)
        results_dict[f'REAL_VOL_VS_RANGE_{window}'] = calculate_realized_vol_vs_intraday_range(returns, high, low, close, window)
    # 因子24: 尾部比率 
    results_dict['TAIL_RATIO_60'] = calculate_tail_ratio(returns, 60)

    # --- 组6: 动量与反转因子 ---
    for window in windows:
        results_dict[f'MOMENTUM_{window}'] = calculate_momentum_factor(returns, window)
        if window <= 10:  # 只对较短周期计算反转因子
            results_dict[f'REVERSAL_{window}'] = -calculate_reversal_factor(returns, window)
    
    results_dict['RSI_14'] = calculate_rsi_factor(close, 14)
    results_dict['MACD_SIGNAL'] = calculate_macd_signal(close, 12, 26, 9)

    # --- 组7: 波动率相关因子 ---
    for window in [10, 20]:
        results_dict[f'GARCH_VOL_PROXY_{window}'] = calculate_garch_volatility_proxy(returns, window)
    
    results_dict['VOL_RISK_PREMIUM'] = calculate_volatility_risk_premium(returns, 5, 20)
    results_dict['VOL_SKEW_30'] = -calculate_volatility_skew_factor(returns, 30)

    # --- 组8: 价值与成长因子 ---
    for window in windows:
        results_dict[f'PB_MOMENTUM_{window}'] = calculate_pb_momentum_factor(pe, returns, window)  # 这里用PE作为代理
    
    results_dict['EARNINGS_QUALITY'] = calculate_earnings_quality_proxy(pe, pe)  # 简化处理

    # --- 组9: 流动性深度因子 ---
    for window in [10, 15]:
        results_dict[f'BID_ASK_PROXY_{window}'] = -calculate_bid_ask_proxy(high, low, volume, window)
        results_dict[f'PRICE_IMPACT_{window}'] = -calculate_price_impact_factor(returns, volume, window)

    # --- 组10: 趋势强度因子 ---
    for window in windows:
        results_dict[f'TREND_CONSISTENCY_{window}'] = calculate_trend_consistency_factor(close, window)
        results_dict[f'BREAKOUT_STRENGTH_{window}'] = calculate_breakout_strength_factor(close, high, low, window)

    # --- 组11: 信息含量因子 ---
    for window in [20, 30]:
        results_dict[f'INFO_RATIO_{window}'] = calculate_information_ratio_factor(returns, index_returns, window)
    
    results_dict['ALPHA_DECAY'] = calculate_alpha_decay_factor(returns, 5, 20)

    # --- 组12: 机器学习启发因子 ---
    for window in [5, 10]:
        results_dict[f'PRICE_ACCEL_{window}'] = calculate_price_acceleration_factor(close, window)
        results_dict[f'VOL_ACCEL_{window}'] = calculate_volume_acceleration_factor(volume, window)
        results_dict[f'REGIME_CHANGE_{window}'] = -calculate_regime_change_factor(returns, volume, window)

    # --- 组13: 高频特征因子 ---
    results_dict['INTRADAY_MOMENTUM'] = calculate_intraday_momentum_factor(open_price, close, high, low)
    results_dict['OVERNIGHT_RISK_10'] = -calculate_overnight_risk_factor(open_price, close, 10)

    # --- 创建最终的DataFrame ---
    results_df = pd.DataFrame(results_dict, index=stock_data.index)
    results_df = results_df.replace([np.inf, -np.inf, np.nan], 0)

    if len(results_df) > MIN_OBS_FOR_CALC:
        return results_df.iloc[MIN_OBS_FOR_CALC-1:-5]
    else:
        return None


# --- 12. 主程序 ---
if __name__ == '__main__':
    print("开始执行创业板股票因子计算...")
    start_time = datetime.datetime.now()

    # --- 加载和预处理数据 ---
    print("加载并预处理数据...")
    stock_basic, stock_daily, daily_basic, stock_merged = load_and_preprocess_data(
        STOCK_BASIC_FILE, STOCK_DAILY_FILE, DAILY_BASIC_FILE
    )

    if stock_merged is None:
        print("数据加载或预处理失败，程序终止。")
        exit()

    print(f"原始数据加载完成。股票行情数据 {len(stock_daily)} 条，合并后数据 {len(stock_merged)} 条。")

    # --- 加载指数数据（用于计算市场下行日因子） ---
    print("加载指数数据...")
    index_data = load_index_data(INDEX_DAILY_FILE)
    if not index_data.empty:
        print(f"指数数据加载完成: {len(index_data)} 条记录")
    else:
        print("警告: 指数数据加载失败，市场下行日因子将填充为0")

    # --- 增量更新处理 ---
    existing_data = None
    last_dates = {}
    if os.path.exists(OUTPUT_FILE):
        print(f"检测到已存在的结果文件: {OUTPUT_FILE}，将进行增量更新。")
        try:
            existing_data = pd.read_csv(OUTPUT_FILE, encoding='utf-8', parse_dates=['trade_date'])
            # 找到每个ts_code已计算的最新日期
            if not existing_data.empty:
                last_dates = existing_data.groupby('ts_code')['trade_date'].max().to_dict()
                print(f"已加载 {len(existing_data)} 条历史计算结果。")
            else:
                print("历史结果文件为空。")
        except Exception as e:
            print(f"加载历史结果文件失败: {e}。将重新计算所有数据。")
            existing_data = None # 出错则重新计算
            last_dates = {}
    else:
        print("未找到历史结果文件，将计算所有数据。")

    # 过滤需要计算的数据
    if last_dates:
        unique_ts_codes_in_merged = stock_merged['ts_code'].unique()
        codes_to_process = []
        for code in unique_ts_codes_in_merged:
            last_calculated_date = last_dates.get(code)
            max_date_for_code = stock_merged[stock_merged['ts_code'] == code]['trade_date'].max()
            if last_calculated_date is None or max_date_for_code > last_calculated_date:
                 codes_to_process.append(code)

        if not codes_to_process:
            print("没有新的数据需要计算。")
            exit()

        print(f"需要处理或更新 {len(codes_to_process)} 个股票的数据。")
        data_to_process = stock_merged[stock_merged['ts_code'].isin(codes_to_process)]

    else:
        # 如果没有历史数据，处理所有数据
        data_to_process = stock_merged
        codes_to_process = data_to_process['ts_code'].unique()
        print("将计算所有股票的数据。")

    if data_to_process.empty:
         print("筛选后没有数据需要处理。")
         exit()

    # --- 并行计算 ---
    print(f"开始并行计算因子 (使用 {N_JOBS} 个进程)...")
    grouped_data = [group for _, group in data_to_process.groupby('ts_code')]

    results_list = Parallel(n_jobs=N_JOBS, verbose=0)(
        delayed(calculate_factors_for_stock)(stock_group, None, index_data)
        for stock_group in tqdm(grouped_data, desc="计算进度", total=len(grouped_data))
    )

    # --- 合并结果 ---
    print("合并计算结果...")
    valid_results = [res for res in results_list if res is not None and isinstance(res, pd.DataFrame) and not res.empty]

    if not valid_results:
        print("所有股票计算均失败或无有效结果。")
        new_results_df = pd.DataFrame() # 创建空的DataFrame
    else:
        new_results_df = pd.concat(valid_results, ignore_index=True)
        new_results_df = new_results_df.sort_values(by=['ts_code', 'trade_date'])
        print(f"计算完成，得到 {len(new_results_df)} 条新因子数据。")

        # --- 筛选出真正"新"的数据行 ---
        if last_dates:
            rows_to_keep = []
            for code, group in new_results_df.groupby('ts_code'):
                last_calculated_date = last_dates.get(code)
                if last_calculated_date:
                    rows_to_keep.append(group[group['trade_date'] > last_calculated_date])
                else:
                    rows_to_keep.append(group)

            if rows_to_keep:
                 new_results_df_filtered = pd.concat(rows_to_keep, ignore_index=True)
                 print(f"筛选出 {len(new_results_df_filtered)} 条需要添加到结果文件的因子数据。")
            else:
                 new_results_df_filtered = pd.DataFrame() # 可能没有更新的数据
                 print("没有需要添加到结果文件的新日期数据。")
            new_results_df = new_results_df_filtered # 使用筛选后的结果

    # --- 合并新旧数据并保存 ---
    if existing_data is not None and not new_results_df.empty:
        print("合并新计算结果与历史结果...")
        # 获取共同列和所有列
        common_cols = existing_data.columns.intersection(new_results_df.columns).tolist()
        all_cols = existing_data.columns.union(new_results_df.columns).tolist()

        # 重新索引
        existing_data_reindexed = existing_data.reindex(columns=all_cols)
        new_results_df_reindexed = new_results_df.reindex(columns=all_cols)
        # 合并并去重
        final_df = pd.concat([existing_data_reindexed, new_results_df_reindexed], ignore_index=True)
        final_df = final_df.drop_duplicates(subset=['ts_code', 'trade_date'], keep='last')
        final_df = final_df.sort_values(by=['ts_code', 'trade_date'])
    elif not new_results_df.empty:
        print("保存首次计算的结果...")
        final_df = new_results_df.sort_values(by=['ts_code', 'trade_date'])
    elif existing_data is not None:
         print("没有新数据需要更新，保留原始结果文件。")
         final_df = existing_data # 保持原样
    else:
         print("没有计算出任何结果，无法保存文件。")
         final_df = pd.DataFrame() # 创建空的DataFrame避免保存时出错
    
    #过滤最近一年的数据
    if not final_df.empty:
        # 获取数据中的最新日期
        latest_date = final_df['trade_date'].max()

        # 计算一年前的日期
        one_year_ago = latest_date - pd.DateOffset(years=1)

        # 过滤出最近一年的数据
        print(f"过滤数据：保留 {one_year_ago.strftime('%Y-%m-%d')} 至 {latest_date.strftime('%Y-%m-%d')} 的数据...")
        final_df_filtered = final_df[final_df['trade_date'] >= one_year_ago].copy()

        # 打印过滤前后的数据量对比
        print(f"过滤前数据量: {len(final_df)} 条")
        print(f"过滤后数据量: {len(final_df_filtered)} 条")
        print(f"删除了 {len(final_df) - len(final_df_filtered)} 条早于一年的历史数据")

        # 更新final_df为过滤后的数据
        final_df = final_df_filtered
    
    # 保存最终结果到CSV
    if not final_df.empty:
        try:
            final_df.to_csv(OUTPUT_FILE, index=False, encoding='utf-8-sig') # 使用utf-8-sig确保Excel能正确打开中文
            print(f"结果已成功保存到: {OUTPUT_FILE}")
            print(f"最终文件包含 {len(final_df)} 条记录。")
        except Exception as e:
            print(f"保存结果文件失败: {e}")
    else:
        print("最终结果为空，未生成或更新文件。")

    end_time = datetime.datetime.now()
    print(f"总耗时: {end_time - start_time}")
