# coding: utf-8
"""
因子挖掘项目主控制脚本
整合数据下载、因子计算、因子挖掘和回测评估的完整流程
"""

import os
import sys
import datetime
import argparse
import pandas as pd
import numpy as np
from typing import List, Optional

# 导入各个模块
try:
    from calculate_stock_factors_trade import main as calculate_factors_main
    from factor_mining_tlrs import TLRSFactorMiner, main as mining_main
    from factor_backtesting_qlib import create_simple_factor_evaluator, main as backtest_main
except ImportError as e:
    print(f"导入模块失败: {e}")
    print("请确保所有模块文件都在当前目录下")
    sys.exit(1)

class FactorMiningPipeline:
    """因子挖掘流水线"""
    
    def __init__(self):
        """初始化流水线"""
        self.project_name = "创业板因子挖掘项目"
        self.start_time = datetime.datetime.now()
        
        # 文件路径配置
        self.data_files = {
            'stock_basic': 'stock_basic_cyb.csv',
            'stock_daily': 'stock_daily_cyb.csv', 
            'daily_basic': 'daily_basic_cyb.csv',
            'index_daily': 'index_daily_cyb.csv'
        }
        
        self.output_files = {
            'factors': 'stock_factors_cyb.csv',
            'mined_factors': 'mined_factors_cyb.csv',
            'backtest_results': 'backtest_results'
        }
        
        print(f"=== {self.project_name} ===")
        print(f"启动时间: {self.start_time.strftime('%Y-%m-%d %H:%M:%S')}")
    
    def check_data_files(self) -> bool:
        """检查数据文件是否存在"""
        print("\n--- 检查数据文件 ---")
        
        missing_files = []
        for file_type, file_path in self.data_files.items():
            if os.path.exists(file_path):
                file_size = os.path.getsize(file_path) / (1024 * 1024)  # MB
                print(f"✓ {file_type}: {file_path} ({file_size:.1f} MB)")
            else:
                print(f"✗ {file_type}: {file_path} (缺失)")
                missing_files.append(file_path)
        
        if missing_files:
            print(f"\n缺失文件: {missing_files}")
            print("请先运行 download_cyb_new.py 下载数据")
            return False
        
        print("所有数据文件检查完成")
        return True
    
    def step1_calculate_factors(self, force_recalculate: bool = False) -> bool:
        """步骤1: 计算因子"""
        print("\n=== 步骤1: 计算因子 ===")
        
        # 检查是否已存在因子文件
        if os.path.exists(self.output_files['factors']) and not force_recalculate:
            print(f"发现已存在的因子文件: {self.output_files['factors']}")
            
            # 检查文件信息
            try:
                factors_df = pd.read_csv(self.output_files['factors'], encoding='utf-8')
                print(f"因子文件包含 {len(factors_df)} 行数据")
                
                factor_cols = [col for col in factors_df.columns 
                              if col not in ['ts_code', 'trade_date', 'target_return']]
                print(f"包含 {len(factor_cols)} 个因子")
                
                choice = input("是否重新计算因子？(y/N): ").strip().lower()
                if choice != 'y':
                    print("跳过因子计算步骤")
                    return True
            except Exception as e:
                print(f"读取因子文件失败: {e}")
                print("将重新计算因子")
        
        # 执行因子计算
        try:
            print("开始计算因子...")
            calculate_factors_main()
            
            # 验证结果
            if os.path.exists(self.output_files['factors']):
                factors_df = pd.read_csv(self.output_files['factors'], encoding='utf-8')
                print(f"因子计算完成，生成 {len(factors_df)} 行数据")
                return True
            else:
                print("因子计算失败，未生成输出文件")
                return False
                
        except Exception as e:
            print(f"因子计算过程中出错: {e}")
            return False
    
    def step2_mine_factors(self, n_factors: int = 10, force_remining: bool = False) -> bool:
        """步骤2: 挖掘因子"""
        print(f"\n=== 步骤2: 挖掘因子 (目标: {n_factors}个) ===")
        
        # 检查因子文件是否存在
        if not os.path.exists(self.output_files['factors']):
            print(f"找不到因子文件: {self.output_files['factors']}")
            print("请先完成步骤1: 计算因子")
            return False
        
        # 检查是否已存在挖掘结果
        if os.path.exists(self.output_files['mined_factors']) and not force_remining:
            print(f"发现已存在的挖掘结果: {self.output_files['mined_factors']}")
            
            try:
                mined_df = pd.read_csv(self.output_files['mined_factors'], encoding='utf-8')
                mined_factor_cols = [col for col in mined_df.columns 
                                   if col.startswith('mined_factor_')]
                print(f"已挖掘 {len(mined_factor_cols)} 个因子")
                
                choice = input("是否重新挖掘因子？(y/N): ").strip().lower()
                if choice != 'y':
                    print("跳过因子挖掘步骤")
                    return True
            except Exception as e:
                print(f"读取挖掘结果失败: {e}")
                print("将重新挖掘因子")
        
        # 执行因子挖掘
        try:
            print("开始挖掘因子...")
            
            # 加载因子数据
            factors_data = pd.read_csv(self.output_files['factors'], encoding='utf-8')
            factors_data['trade_date'] = pd.to_datetime(factors_data['trade_date'])
            
            # 初始化挖掘器
            miner = TLRSFactorMiner(factors_data)
            
            # 挖掘因子
            mined_factors = miner.mine_factors(n_factors=n_factors)
            
            # 保存结果
            output_file = miner.save_mined_factors(mined_factors)
            
            # 绘制训练历史
            miner.plot_training_history()
            
            print(f"因子挖掘完成，结果保存到: {output_file}")
            return True
            
        except Exception as e:
            print(f"因子挖掘过程中出错: {e}")
            return False
    
    def step3_evaluate_factors(self, evaluation_method: str = 'simple') -> bool:
        """步骤3: 评估因子"""
        print(f"\n=== 步骤3: 评估因子 ({evaluation_method}方式) ===")
        
        # 检查挖掘结果是否存在
        if not os.path.exists(self.output_files['mined_factors']):
            print(f"找不到挖掘结果: {self.output_files['mined_factors']}")
            print("请先完成步骤2: 挖掘因子")
            return False
        
        try:
            if evaluation_method == 'simple':
                # 简单评估
                print("使用简单评估方法...")
                results = create_simple_factor_evaluator(self.output_files['mined_factors'])
                
                if results:
                    print("因子评估完成")
                    return True
                else:
                    print("因子评估失败")
                    return False
                    
            elif evaluation_method == 'full':
                # 完整回测
                print("使用完整回测方法...")
                backtest_main()
                return True
                
            else:
                print(f"不支持的评估方法: {evaluation_method}")
                return False
                
        except Exception as e:
            print(f"因子评估过程中出错: {e}")
            return False
    
    def run_full_pipeline(self, n_factors: int = 10, 
                         evaluation_method: str = 'simple',
                         force_recalculate: bool = False,
                         force_remining: bool = False) -> bool:
        """运行完整流水线"""
        print(f"\n=== 运行完整因子挖掘流水线 ===")
        print(f"目标挖掘因子数量: {n_factors}")
        print(f"评估方法: {evaluation_method}")
        
        # 检查数据文件
        if not self.check_data_files():
            return False
        
        # 步骤1: 计算因子
        if not self.step1_calculate_factors(force_recalculate):
            print("步骤1失败，流水线终止")
            return False
        
        # 步骤2: 挖掘因子
        if not self.step2_mine_factors(n_factors, force_remining):
            print("步骤2失败，流水线终止")
            return False
        
        # 步骤3: 评估因子
        if not self.step3_evaluate_factors(evaluation_method):
            print("步骤3失败，但前面步骤已完成")
            return False
        
        # 流水线完成
        end_time = datetime.datetime.now()
        duration = end_time - self.start_time
        
        print(f"\n=== 流水线执行完成 ===")
        print(f"开始时间: {self.start_time.strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"结束时间: {end_time.strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"总耗时: {duration}")
        
        # 生成项目总结
        self.generate_project_summary()
        
        return True
    
    def generate_project_summary(self):
        """生成项目总结报告"""
        print("\n=== 生成项目总结 ===")
        
        summary = {
            'project_name': self.project_name,
            'execution_time': {
                'start': self.start_time.strftime('%Y-%m-%d %H:%M:%S'),
                'end': datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                'duration': str(datetime.datetime.now() - self.start_time)
            },
            'files_generated': [],
            'statistics': {}
        }
        
        # 检查生成的文件
        for file_type, file_path in self.output_files.items():
            if os.path.exists(file_path):
                if file_path.endswith('.csv'):
                    try:
                        df = pd.read_csv(file_path, encoding='utf-8')
                        summary['files_generated'].append({
                            'type': file_type,
                            'path': file_path,
                            'size_mb': round(os.path.getsize(file_path) / (1024*1024), 2),
                            'rows': len(df),
                            'columns': len(df.columns)
                        })
                    except:
                        summary['files_generated'].append({
                            'type': file_type,
                            'path': file_path,
                            'size_mb': round(os.path.getsize(file_path) / (1024*1024), 2)
                        })
                else:
                    summary['files_generated'].append({
                        'type': file_type,
                        'path': file_path,
                        'is_directory': os.path.isdir(file_path)
                    })
        
        # 统计信息
        if os.path.exists(self.output_files['factors']):
            try:
                factors_df = pd.read_csv(self.output_files['factors'], encoding='utf-8')
                factor_cols = [col for col in factors_df.columns 
                              if col not in ['ts_code', 'trade_date', 'target_return']]
                summary['statistics']['total_factors_calculated'] = len(factor_cols)
                summary['statistics']['total_data_points'] = len(factors_df)
                summary['statistics']['unique_stocks'] = factors_df['ts_code'].nunique()
            except:
                pass
        
        if os.path.exists(self.output_files['mined_factors']):
            try:
                mined_df = pd.read_csv(self.output_files['mined_factors'], encoding='utf-8')
                mined_factor_cols = [col for col in mined_df.columns 
                                   if col.startswith('mined_factor_')]
                summary['statistics']['factors_mined'] = len(mined_factor_cols)
            except:
                pass
        
        # 保存总结到文件
        import json
        summary_file = f"project_summary_{datetime.datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        with open(summary_file, 'w', encoding='utf-8') as f:
            json.dump(summary, f, ensure_ascii=False, indent=2)
        
        print(f"项目总结已保存到: {summary_file}")
        
        # 打印总结
        print("\n--- 项目执行总结 ---")
        print(f"项目名称: {summary['project_name']}")
        print(f"执行时间: {summary['execution_time']['duration']}")
        print(f"生成文件数量: {len(summary['files_generated'])}")
        
        if 'total_factors_calculated' in summary['statistics']:
            print(f"计算因子数量: {summary['statistics']['total_factors_calculated']}")
        if 'factors_mined' in summary['statistics']:
            print(f"挖掘因子数量: {summary['statistics']['factors_mined']}")
        if 'unique_stocks' in summary['statistics']:
            print(f"覆盖股票数量: {summary['statistics']['unique_stocks']}")

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='因子挖掘项目主控制脚本')
    parser.add_argument('--step', type=str, choices=['1', '2', '3', 'all'], 
                       default='all', help='执行步骤 (1:计算因子, 2:挖掘因子, 3:评估因子, all:全部)')
    parser.add_argument('--n-factors', type=int, default=10, 
                       help='挖掘因子数量 (默认: 10)')
    parser.add_argument('--evaluation', type=str, choices=['simple', 'full'], 
                       default='simple', help='评估方法 (默认: simple)')
    parser.add_argument('--force-recalculate', action='store_true', 
                       help='强制重新计算因子')
    parser.add_argument('--force-remining', action='store_true', 
                       help='强制重新挖掘因子')
    
    args = parser.parse_args()
    
    # 创建流水线实例
    pipeline = FactorMiningPipeline()
    
    # 根据参数执行相应步骤
    if args.step == '1':
        success = pipeline.step1_calculate_factors(args.force_recalculate)
    elif args.step == '2':
        success = pipeline.step2_mine_factors(args.n_factors, args.force_remining)
    elif args.step == '3':
        success = pipeline.step3_evaluate_factors(args.evaluation)
    elif args.step == 'all':
        success = pipeline.run_full_pipeline(
            n_factors=args.n_factors,
            evaluation_method=args.evaluation,
            force_recalculate=args.force_recalculate,
            force_remining=args.force_remining
        )
    else:
        print(f"不支持的步骤: {args.step}")
        success = False
    
    # 退出
    if success:
        print("\n项目执行成功！")
        sys.exit(0)
    else:
        print("\n项目执行失败！")
        sys.exit(1)

if __name__ == "__main__":
    main()
