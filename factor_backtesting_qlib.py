# coding: utf-8
"""
基于Qlib的专业因子回测框架
用于评测挖掘出的因子的投资性能
"""

import pandas as pd
import numpy as np
import os
import warnings
import datetime
from typing import List, Dict, Optional, Tuple
import pickle
import json
import matplotlib.pyplot as plt
import seaborn as sns
from scipy import stats
from sklearn.metrics import mean_squared_error
import qlib
from qlib.config import REG_CN
from qlib.data import D
from qlib.model.trainer import task_train
from qlib.workflow import R
from qlib.utils import init_instance_by_config
from qlib.workflow.record_temp import SignalRecord, PortAnaRecord
from qlib.contrib.model.gbdt import LGBModel
from qlib.contrib.data.handler import Alpha158
from qlib.contrib.strategy import TopKDropoutStrategy
from qlib.contrib.evaluate import (
    backtest as normal_backtest,
    risk_analysis
)
from qlib.backtest import backtest, executor
from qlib.contrib.report import analysis_model, analysis_position

warnings.filterwarnings('ignore')

# --- 配置参数 ---
MINED_FACTORS_FILE = 'mined_factors_cyb.csv'
BACKTEST_RESULTS_DIR = 'backtest_results'
QLIB_DATA_DIR = './qlib_data'

# 回测参数
START_TIME = '2020-01-01'
END_TIME = '2024-12-31'
BENCHMARK = 'SH000905'  # 中证500作为基准
MARKET = 'csi300'  # 股票池

# 投资组合参数
TOPK = 30  # 选股数量
N_DROP = 5  # 每期调仓时卖出股票数量
REBALANCE_FREQ = 'week'  # 调仓频率

class FactorBacktester:
    """因子回测系统"""
    
    def __init__(self, factors_file: str = MINED_FACTORS_FILE):
        """
        初始化回测系统
        
        Args:
            factors_file: 因子数据文件路径
        """
        self.factors_file = factors_file
        self.factors_data = None
        self.backtest_results = {}
        
        # 确保输出目录存在
        os.makedirs(BACKTEST_RESULTS_DIR, exist_ok=True)
        
        # 初始化qlib
        self._init_qlib()
        
        # 加载因子数据
        self._load_factor_data()
    
    def _init_qlib(self):
        """初始化Qlib"""
        try:
            print("初始化Qlib...")
            qlib.init(provider_uri=QLIB_DATA_DIR, region=REG_CN)
            print("Qlib初始化成功")
        except Exception as e:
            print(f"Qlib初始化失败: {e}")
            print("将尝试使用在线数据...")
            try:
                qlib.init(provider_uri='~/.qlib/qlib_data/cn_data', region=REG_CN)
                print("使用在线数据初始化成功")
            except Exception as e2:
                print(f"在线数据初始化也失败: {e2}")
                raise Exception("无法初始化Qlib，请检查数据配置")
    
    def _load_factor_data(self):
        """加载因子数据"""
        if not os.path.exists(self.factors_file):
            raise FileNotFoundError(f"找不到因子文件: {self.factors_file}")
        
        print(f"加载因子数据: {self.factors_file}")
        self.factors_data = pd.read_csv(self.factors_file, encoding='utf-8')
        self.factors_data['trade_date'] = pd.to_datetime(self.factors_data['trade_date'])
        
        # 获取因子列名
        self.factor_columns = [col for col in self.factors_data.columns 
                              if col.startswith('mined_factor_')]
        
        print(f"加载完成，共 {len(self.factors_data)} 行数据，{len(self.factor_columns)} 个因子")
    
    def prepare_qlib_data(self, factor_names: List[str] = None) -> pd.DataFrame:
        """
        准备Qlib格式的数据
        
        Args:
            factor_names: 要使用的因子名称列表，默认使用所有挖掘的因子
            
        Returns:
            格式化后的数据DataFrame
        """
        if factor_names is None:
            factor_names = self.factor_columns
        
        print(f"准备Qlib数据，使用因子: {factor_names}")
        
        # 转换为Qlib需要的格式
        qlib_data = []
        
        for ts_code in self.factors_data['ts_code'].unique():
            stock_data = self.factors_data[self.factors_data['ts_code'] == ts_code].copy()
            stock_data = stock_data.sort_values('trade_date')
            
            for _, row in stock_data.iterrows():
                qlib_row = {
                    'instrument': ts_code.replace('.SZ', '.SZSE').replace('.SH', '.SHSE'),
                    'datetime': row['trade_date'],
                    'label': row.get('target_return', 0) / 100,  # 转换为小数
                }
                
                # 添加因子值
                for factor_name in factor_names:
                    qlib_row[factor_name] = row.get(factor_name, 0)
                
                qlib_data.append(qlib_row)
        
        qlib_df = pd.DataFrame(qlib_data)
        print(f"Qlib数据准备完成，共 {len(qlib_df)} 行")
        
        return qlib_df
    
    def create_factor_model_config(self, factor_names: List[str]) -> Dict:
        """
        创建因子模型配置
        
        Args:
            factor_names: 因子名称列表
            
        Returns:
            模型配置字典
        """
        # 特征配置
        feature_config = []
        for factor_name in factor_names:
            feature_config.append(f'${factor_name}')
        
        # 数据处理器配置
        data_handler_config = {
            "class": "Alpha158",
            "module_path": "qlib.contrib.data.handler",
            "kwargs": {
                "start_time": START_TIME,
                "end_time": END_TIME,
                "fit_start_time": START_TIME,
                "fit_end_time": END_TIME,
                "instruments": MARKET,
                "infer_processors": [
                    {"class": "RobustZScoreNorm", 
                     "kwargs": {"fields_group": "feature", "clip_outlier": True}},
                    {"class": "Fillna", "kwargs": {"fields_group": "feature"}}
                ],
                "learn_processors": [
                    {"class": "DropnaLabel"},
                    {"class": "CSRankNorm", "kwargs": {"fields_group": "label"}},
                ],
                "label": ["Ref($close, -2) / Ref($close, -1) - 1"]
            }
        }
        
        # 模型配置
        model_config = {
            "class": "LGBModel",
            "module_path": "qlib.contrib.model.gbdt",
            "kwargs": {
                "loss": "mse",
                "colsample_bytree": 0.8879,
                "learning_rate": 0.0421,
                "subsample": 0.8789,
                "lambda_l1": 205.6999,
                "lambda_l2": 580.9768,
                "max_depth": 8,
                "num_leaves": 210,
                "num_threads": 20,
            }
        }
        
        # 任务配置
        task_config = {
            "model": model_config,
            "dataset": {
                "class": "DatasetH",
                "module_path": "qlib.data.dataset",
                "kwargs": {
                    "handler": data_handler_config,
                    "segments": {
                        "train": (START_TIME, "2022-12-31"),
                        "valid": ("2023-01-01", "2023-12-31"), 
                        "test": ("2024-01-01", END_TIME),
                    },
                },
            },
        }
        
        return task_config
    
    def train_factor_model(self, factor_names: List[str], model_name: str = None) -> str:
        """
        训练因子模型
        
        Args:
            factor_names: 使用的因子名称列表
            model_name: 模型名称
            
        Returns:
            训练好的模型记录ID
        """
        if model_name is None:
            model_name = f"factor_model_{datetime.datetime.now().strftime('%Y%m%d_%H%M%S')}"
        
        print(f"开始训练因子模型: {model_name}")
        print(f"使用因子: {factor_names}")
        
        # 创建模型配置
        task_config = self.create_factor_model_config(factor_names)
        
        try:
            # 训练模型
            with R.start(experiment_name="factor_mining"):
                model = task_train(task_config, experiment_name="factor_mining")
                rid = R.get_recorder().id
                
                print(f"模型训练完成，记录ID: {rid}")
                return rid
                
        except Exception as e:
            print(f"模型训练失败: {e}")
            return None
    
    def backtest_single_factor(self, factor_name: str, 
                              start_time: str = START_TIME,
                              end_time: str = END_TIME) -> Dict:
        """
        单因子回测
        
        Args:
            factor_name: 因子名称
            start_time: 开始时间
            end_time: 结束时间
            
        Returns:
            回测结果字典
        """
        print(f"开始单因子回测: {factor_name}")
        
        try:
            # 训练模型
            rid = self.train_factor_model([factor_name], f"single_{factor_name}")
            
            if rid is None:
                return {"error": "模型训练失败"}
            
            # 创建策略配置
            strategy_config = {
                "class": "TopKDropoutStrategy",
                "module_path": "qlib.contrib.strategy",
                "kwargs": {
                    "model": rid,
                    "dataset": self.create_factor_model_config([factor_name])["dataset"],
                    "topk": TOPK,
                    "n_drop": N_DROP,
                }
            }
            
            # 创建回测配置
            backtest_config = {
                "start_time": start_time,
                "end_time": end_time,
                "account": *********,  # 1亿初始资金
                "benchmark": BENCHMARK,
                "exchange_kwargs": {
                    "freq": "day",
                    "limit_threshold": 0.095,
                    "deal_price": "close",
                    "open_cost": 0.0005,
                    "close_cost": 0.0015,
                    "min_cost": 5,
                },
            }
            
            # 执行回测
            with R.start(experiment_name="factor_backtest"):
                strategy = init_instance_by_config(strategy_config)
                backtest_result = normal_backtest(strategy, **backtest_config)
                
                # 计算性能指标
                performance = self._calculate_performance_metrics(backtest_result)
                
                result = {
                    "factor_name": factor_name,
                    "backtest_result": backtest_result,
                    "performance": performance,
                    "success": True
                }
                
                print(f"单因子回测完成: {factor_name}")
                return result
                
        except Exception as e:
            print(f"单因子回测失败 {factor_name}: {e}")
            return {"factor_name": factor_name, "error": str(e), "success": False}
    
    def backtest_multi_factors(self, factor_names: List[str],
                              combination_name: str = "multi_factors",
                              start_time: str = START_TIME,
                              end_time: str = END_TIME) -> Dict:
        """
        多因子组合回测
        
        Args:
            factor_names: 因子名称列表
            combination_name: 组合名称
            start_time: 开始时间
            end_time: 结束时间
            
        Returns:
            回测结果字典
        """
        print(f"开始多因子回测: {combination_name}")
        print(f"使用因子: {factor_names}")
        
        try:
            # 训练多因子模型
            rid = self.train_factor_model(factor_names, f"multi_{combination_name}")
            
            if rid is None:
                return {"error": "多因子模型训练失败"}
            
            # 创建策略配置
            strategy_config = {
                "class": "TopKDropoutStrategy", 
                "module_path": "qlib.contrib.strategy",
                "kwargs": {
                    "model": rid,
                    "dataset": self.create_factor_model_config(factor_names)["dataset"],
                    "topk": TOPK,
                    "n_drop": N_DROP,
                }
            }
            
            # 回测配置
            backtest_config = {
                "start_time": start_time,
                "end_time": end_time,
                "account": *********,
                "benchmark": BENCHMARK,
                "exchange_kwargs": {
                    "freq": "day", 
                    "limit_threshold": 0.095,
                    "deal_price": "close",
                    "open_cost": 0.0005,
                    "close_cost": 0.0015,
                    "min_cost": 5,
                },
            }
            
            # 执行回测
            with R.start(experiment_name="multi_factor_backtest"):
                strategy = init_instance_by_config(strategy_config)
                backtest_result = normal_backtest(strategy, **backtest_config)
                
                # 计算性能指标
                performance = self._calculate_performance_metrics(backtest_result)
                
                result = {
                    "combination_name": combination_name,
                    "factor_names": factor_names,
                    "backtest_result": backtest_result,
                    "performance": performance,
                    "success": True
                }
                
                print(f"多因子回测完成: {combination_name}")
                return result
                
        except Exception as e:
            print(f"多因子回测失败 {combination_name}: {e}")
            return {
                "combination_name": combination_name, 
                "factor_names": factor_names,
                "error": str(e), 
                "success": False
            }
    
    def _calculate_performance_metrics(self, backtest_result) -> Dict:
        """
        计算性能指标
        
        Args:
            backtest_result: 回测结果
            
        Returns:
            性能指标字典
        """
        try:
            # 提取收益序列
            returns = backtest_result['return']
            benchmark_returns = backtest_result.get('bench', pd.Series())
            
            # 计算基本指标
            total_return = (returns + 1).prod() - 1
            annual_return = (1 + total_return) ** (252 / len(returns)) - 1
            volatility = returns.std() * np.sqrt(252)
            sharpe_ratio = annual_return / volatility if volatility != 0 else 0
            
            # 最大回撤
            cumulative_returns = (returns + 1).cumprod()
            rolling_max = cumulative_returns.expanding().max()
            drawdown = (cumulative_returns - rolling_max) / rolling_max
            max_drawdown = drawdown.min()
            
            # 胜率
            win_rate = (returns > 0).sum() / len(returns)
            
            # 与基准的比较
            if not benchmark_returns.empty:
                excess_returns = returns - benchmark_returns
                tracking_error = excess_returns.std() * np.sqrt(252)
                information_ratio = excess_returns.mean() * np.sqrt(252) / tracking_error if tracking_error != 0 else 0
                
                benchmark_total_return = (benchmark_returns + 1).prod() - 1
                alpha = total_return - benchmark_total_return
            else:
                tracking_error = 0
                information_ratio = 0
                alpha = 0
            
            # 下行风险指标
            downside_returns = returns[returns < 0]
            downside_deviation = downside_returns.std() * np.sqrt(252) if len(downside_returns) > 0 else 0
            sortino_ratio = annual_return / downside_deviation if downside_deviation != 0 else 0
            
            # Calmar比率
            calmar_ratio = annual_return / abs(max_drawdown) if max_drawdown != 0 else 0
            
            metrics = {
                'total_return': total_return,
                'annual_return': annual_return,
                'volatility': volatility,
                'sharpe_ratio': sharpe_ratio,
                'max_drawdown': max_drawdown,
                'win_rate': win_rate,
                'tracking_error': tracking_error,
                'information_ratio': information_ratio,
                'alpha': alpha,
                'downside_deviation': downside_deviation,
                'sortino_ratio': sortino_ratio,
                'calmar_ratio': calmar_ratio,
            }
            
            return metrics
            
        except Exception as e:
            print(f"计算性能指标时出错: {e}")
            return {}
    
    def run_comprehensive_backtest(self, specified_factors: List[str] = None) -> Dict:
        """
        运行综合回测
        
        Args:
            specified_factors: 指定要测试的因子列表，默认测试所有挖掘的因子
            
        Returns:
            综合回测结果
        """
        print("=== 开始综合因子回测 ===")
        
        if specified_factors is None:
            test_factors = self.factor_columns
        else:
            test_factors = [f for f in specified_factors if f in self.factor_columns]
        
        if not test_factors:
            print("没有找到可测试的因子")
            return {}
        
        print(f"将测试 {len(test_factors)} 个因子")
        
        results = {
            'single_factor_results': {},
            'multi_factor_results': {},
            'factor_rankings': {},
            'summary_statistics': {}
        }
        
        # 1. 单因子回测
        print("\n--- 单因子回测 ---")
        single_factor_performance = {}
        
        for factor_name in test_factors:
            print(f"测试因子: {factor_name}")
            result = self.backtest_single_factor(factor_name)
            results['single_factor_results'][factor_name] = result
            
            if result.get('success', False):
                performance = result['performance']
                single_factor_performance[factor_name] = performance
        
        # 2. 因子排名
        print("\n--- 因子排名分析 ---")
        if single_factor_performance:
            rankings = self._rank_factors(single_factor_performance)
            results['factor_rankings'] = rankings
            
            # 选择表现最好的因子进行组合测试
            top_factors = rankings['sharpe_ratio'][:5]  # 选择夏普比率最高的5个因子
            top_factor_names = [item['factor_name'] for item in top_factors]
            
            print(f"表现最佳的因子: {top_factor_names}")
            
            # 3. 多因子组合回测
            print("\n--- 多因子组合回测 ---")
            multi_result = self.backtest_multi_factors(
                top_factor_names, 
                "top_factors_combination"
            )
            results['multi_factor_results']['top_combination'] = multi_result
        
        # 4. 汇总统计
        print("\n--- 生成汇总统计 ---")
        summary = self._generate_summary_statistics(results)
        results['summary_statistics'] = summary
        
        # 5. 保存结果
        self._save_backtest_results(results)
        
        print("=== 综合回测完成 ===")
        return results
    
    def _rank_factors(self, performance_dict: Dict) -> Dict:
        """
        对因子进行排名
        
        Args:
            performance_dict: 因子性能字典
            
        Returns:
            排名结果
        """
        rankings = {}
        
        # 按不同指标排名
        metrics_to_rank = [
            'sharpe_ratio', 'annual_return', 'information_ratio', 
            'calmar_ratio', 'sortino_ratio'
        ]
        
        for metric in metrics_to_rank:
            metric_values = []
            for factor_name, performance in performance_dict.items():
                if metric in performance:
                    metric_values.append({
                        'factor_name': factor_name,
                        'value': performance[metric]
                    })
            
            # 按值降序排序
            metric_values.sort(key=lambda x: x['value'], reverse=True)
            rankings[metric] = metric_values
        
        return rankings
    
    def _generate_summary_statistics(self, results: Dict) -> Dict:
        """
        生成汇总统计
        
        Args:
            results: 回测结果
            
        Returns:
            汇总统计
        """
        summary = {
            'total_factors_tested': 0,
            'successful_factors': 0,
            'failed_factors': 0,
            'best_single_factor': None,
            'multi_factor_performance': None,
            'performance_distribution': {}
        }
        
        # 统计单因子结果
        single_results = results.get('single_factor_results', {})
        summary['total_factors_tested'] = len(single_results)
        
        successful_performances = {}
        for factor_name, result in single_results.items():
            if result.get('success', False):
                summary['successful_factors'] += 1
                successful_performances[factor_name] = result['performance']
            else:
                summary['failed_factors'] += 1
        
        # 找出最佳单因子
        if successful_performances:
            best_factor = max(successful_performances.items(), 
                            key=lambda x: x[1].get('sharpe_ratio', 0))
            summary['best_single_factor'] = {
                'name': best_factor[0],
                'performance': best_factor[1]
            }
            
            # 性能分布统计
            sharpe_ratios = [perf.get('sharpe_ratio', 0) for perf in successful_performances.values()]
            summary['performance_distribution'] = {
                'sharpe_ratio_mean': np.mean(sharpe_ratios),
                'sharpe_ratio_std': np.std(sharpe_ratios),
                'sharpe_ratio_median': np.median(sharpe_ratios),
                'sharpe_ratio_max': np.max(sharpe_ratios),
                'sharpe_ratio_min': np.min(sharpe_ratios)
            }
        
        # 多因子组合性能
        multi_results = results.get('multi_factor_results', {})
        if multi_results:
            for combo_name, combo_result in multi_results.items():
                if combo_result.get('success', False):
                    summary['multi_factor_performance'] = combo_result['performance']
                    break
        
        return summary
    
    def _save_backtest_results(self, results: Dict):
        """保存回测结果"""
        timestamp = datetime.datetime.now().strftime('%Y%m%d_%H%M%S')
        
        # 保存完整结果到pickle文件
        pickle_file = os.path.join(BACKTEST_RESULTS_DIR, f'backtest_results_{timestamp}.pkl')
        with open(pickle_file, 'wb') as f:
            pickle.dump(results, f)
        
        # 保存汇总结果到JSON文件
        json_file = os.path.join(BACKTEST_RESULTS_DIR, f'backtest_summary_{timestamp}.json')
        summary_for_json = {
            'summary_statistics': results['summary_statistics'],
            'factor_rankings': results['factor_rankings']
        }
        
        with open(json_file, 'w', encoding='utf-8') as f:
            json.dump(summary_for_json, f, ensure_ascii=False, indent=2, default=str)
        
        # 保存性能报告到CSV
        csv_file = os.path.join(BACKTEST_RESULTS_DIR, f'factor_performance_{timestamp}.csv')
        performance_data = []
        
        for factor_name, result in results['single_factor_results'].items():
            if result.get('success', False):
                perf_row = {'factor_name': factor_name}
                perf_row.update(result['performance'])
                performance_data.append(perf_row)
        
        if performance_data:
            performance_df = pd.DataFrame(performance_data)
            performance_df.to_csv(csv_file, index=False, encoding='utf-8-sig')
        
        print(f"回测结果已保存：")
        print(f"  完整结果: {pickle_file}")
        print(f"  汇总结果: {json_file}")
        print(f"  性能报告: {csv_file}")
    
    def generate_backtest_report(self, results: Dict, output_file: str = None):
        """
        生成回测报告
        
        Args:
            results: 回测结果
            output_file: 输出文件名
        """
        if output_file is None:
            timestamp = datetime.datetime.now().strftime('%Y%m%d_%H%M%S')
            output_file = os.path.join(BACKTEST_RESULTS_DIR, f'backtest_report_{timestamp}.png')
        
        # 创建图表
        fig, axes = plt.subplots(2, 3, figsize=(20, 12))
        fig.suptitle('因子回测性能报告', fontsize=16, y=0.98)
        
        # 收集单因子性能数据
        single_performances = {}
        for factor_name, result in results['single_factor_results'].items():
            if result.get('success', False):
                single_performances[factor_name] = result['performance']
        
        if not single_performances:
            plt.text(0.5, 0.5, '没有成功的回测结果', ha='center', va='center')
            plt.savefig(output_file, dpi=300, bbox_inches='tight')
            return
        
        # 1. 夏普比率分布
        sharpe_ratios = [perf['sharpe_ratio'] for perf in single_performances.values()]
        axes[0, 0].hist(sharpe_ratios, bins=10, alpha=0.7, color='skyblue', edgecolor='black')
        axes[0, 0].set_title('夏普比率分布')
        axes[0, 0].set_xlabel('夏普比率')
        axes[0, 0].set_ylabel('频数')
        axes[0, 0].grid(True, alpha=0.3)
        
        # 2. 年化收益率分布  
        annual_returns = [perf['annual_return'] for perf in single_performances.values()]
        axes[0, 1].hist(annual_returns, bins=10, alpha=0.7, color='lightgreen', edgecolor='black')
        axes[0, 1].set_title('年化收益率分布')
        axes[0, 1].set_xlabel('年化收益率')
        axes[0, 1].set_ylabel('频数')
        axes[0, 1].grid(True, alpha=0.3)
        
        # 3. 最大回撤分布
        max_drawdowns = [abs(perf['max_drawdown']) for perf in single_performances.values()]
        axes[0, 2].hist(max_drawdowns, bins=10, alpha=0.7, color='lightcoral', edgecolor='black')
        axes[0, 2].set_title('最大回撤分布')
        axes[0, 2].set_xlabel('最大回撤')
        axes[0, 2].set_ylabel('频数')
        axes[0, 2].grid(True, alpha=0.3)
        
        # 4. 风险收益散点图
        volatilities = [perf['volatility'] for perf in single_performances.values()]
        axes[1, 0].scatter(volatilities, annual_returns, alpha=0.6, s=50)
        axes[1, 0].set_title('风险收益散点图')
        axes[1, 0].set_xlabel('波动率')
        axes[1, 0].set_ylabel('年化收益率')
        axes[1, 0].grid(True, alpha=0.3)
        
        # 5. 因子表现排名（前10）
        if 'factor_rankings' in results and 'sharpe_ratio' in results['factor_rankings']:
            top_factors = results['factor_rankings']['sharpe_ratio'][:10]
            factor_names = [f"F{i+1}" for i in range(len(top_factors))]  # 简化因子名称显示
            sharpe_values = [item['value'] for item in top_factors]
            
            bars = axes[1, 1].bar(range(len(factor_names)), sharpe_values, 
                                alpha=0.7, color='orange')
            axes[1, 1].set_title('因子夏普比率排名（前10）')
            axes[1, 1].set_xlabel('因子')
            axes[1, 1].set_ylabel('夏普比率')
            axes[1, 1].set_xticks(range(len(factor_names)))
            axes[1, 1].set_xticklabels(factor_names, rotation=45)
            axes[1, 1].grid(True, alpha=0.3)
            
            # 在柱状图上显示数值
            for i, bar in enumerate(bars):
                height = bar.get_height()
                axes[1, 1].text(bar.get_x() + bar.get_width()/2., height,
                               f'{height:.3f}', ha='center', va='bottom', fontsize=8)
        
        # 6. 多因子vs单因子对比
        if 'multi_factor_results' in results and results['multi_factor_results']:
            multi_performance = None
            for combo_result in results['multi_factor_results'].values():
                if combo_result.get('success', False):
                    multi_performance = combo_result['performance']
                    break
            
            if multi_performance and single_performances:
                # 比较多因子和最佳单因子的性能
                best_single_sharpe = max(perf['sharpe_ratio'] for perf in single_performances.values())
                multi_sharpe = multi_performance.get('sharpe_ratio', 0)
                
                categories = ['最佳单因子', '多因子组合']
                sharpe_values = [best_single_sharpe, multi_sharpe]
                
                bars = axes[1, 2].bar(categories, sharpe_values, 
                                    alpha=0.7, color=['lightblue', 'lightcoral'])
                axes[1, 2].set_title('单因子 vs 多因子组合')
                axes[1, 2].set_ylabel('夏普比率')
                axes[1, 2].grid(True, alpha=0.3)
                
                # 显示数值
                for i, bar in enumerate(bars):
                    height = bar.get_height()
                    axes[1, 2].text(bar.get_x() + bar.get_width()/2., height,
                                   f'{height:.3f}', ha='center', va='bottom')
        else:
            axes[1, 2].text(0.5, 0.5, '没有多因子回测结果', ha='center', va='center')
            axes[1, 2].set_title('单因子 vs 多因子组合')
        
        plt.tight_layout()
        plt.savefig(output_file, dpi=300, bbox_inches='tight')
        print(f"回测报告已保存: {output_file}")
        
        # 显示图表
        plt.show()

def create_simple_factor_evaluator(factors_file: str = MINED_FACTORS_FILE):
    """
    创建简单的因子评估器（不依赖qlib）
    
    Args:
        factors_file: 因子文件路径
        
    Returns:
        简单评估结果
    """
    if not os.path.exists(factors_file):
        print(f"找不到因子文件: {factors_file}")
        return None
    
    print("=== 简单因子评估（不使用qlib） ===")
    
    # 加载数据
    data = pd.read_csv(factors_file, encoding='utf-8')
    data['trade_date'] = pd.to_datetime(data['trade_date'])
    
    # 获取因子列
    factor_cols = [col for col in data.columns if col.startswith('mined_factor_')]
    
    if not factor_cols:
        print("没有找到挖掘的因子")
        return None
    
    print(f"评估 {len(factor_cols)} 个因子")
    
    results = {}
    
    for factor_col in factor_cols:
        # 计算IC和Rank IC
        valid_data = data.dropna(subset=[factor_col, 'target_return'])
        
        if len(valid_data) < 100:
            continue
        
        factor_values = valid_data[factor_col].values
        target_values = valid_data['target_return'].values
        
        # IC (Pearson correlation)
        ic, ic_p_value = stats.pearsonr(factor_values, target_values)
        
        # Rank IC (Spearman correlation) 
        rank_ic, rank_ic_p_value = stats.spearmanr(factor_values, target_values)
        
        # 按时间分段计算IC稳定性
        valid_data = valid_data.sort_values('trade_date')
        n_periods = 5
        period_size = len(valid_data) // n_periods
        period_ics = []
        
        for i in range(n_periods):
            start_idx = i * period_size
            end_idx = (i + 1) * period_size if i < n_periods - 1 else len(valid_data)
            
            period_data = valid_data.iloc[start_idx:end_idx]
            if len(period_data) > 10:
                period_ic, _ = stats.pearsonr(
                    period_data[factor_col].values,
                    period_data['target_return'].values
                )
                if not np.isnan(period_ic):
                    period_ics.append(period_ic)
        
        ic_stability = 1 - np.std(period_ics) if len(period_ics) > 1 else 0
        
        results[factor_col] = {
            'ic': ic,
            'ic_p_value': ic_p_value,
            'rank_ic': rank_ic, 
            'rank_ic_p_value': rank_ic_p_value,
            'ic_stability': ic_stability,
            'sample_size': len(valid_data),
            'abs_ic': abs(ic),
            'abs_rank_ic': abs(rank_ic)
        }
    
    # 排序结果
    sorted_results = sorted(results.items(), key=lambda x: x[1]['abs_ic'], reverse=True)
    
    print("\n=== 因子评估结果（按IC绝对值排序） ===")
    print(f"{'因子名称':<20} {'IC':<8} {'Rank IC':<8} {'稳定性':<8} {'样本数':<8}")
    print("-" * 60)
    
    for factor_name, metrics in sorted_results[:10]:  # 显示前10个
        print(f"{factor_name:<20} {metrics['ic']:<8.4f} {metrics['rank_ic']:<8.4f} "
              f"{metrics['ic_stability']:<8.4f} {metrics['sample_size']:<8}")
    
    return results

def main():
    """主函数"""
    print("=== 因子回测评估系统 ===")
    
    # 检查挖掘的因子文件是否存在
    if not os.path.exists(MINED_FACTORS_FILE):
        print(f"找不到挖掘的因子文件: {MINED_FACTORS_FILE}")
        print("请先运行 factor_mining_tlrs.py 挖掘因子")
        
        # 尝试简单评估现有因子数据
        factors_file = 'stock_factors_cyb.csv'
        if os.path.exists(factors_file):
            print(f"尝试评估基础因子文件: {factors_file}")
            simple_results = create_simple_factor_evaluator(factors_file)
            return simple_results
        else:
            print("也找不到基础因子文件，请先运行因子计算")
            return None
    
    # 选择评估方式
    print("\n请选择评估方式:")
    print("1. 简单评估（不依赖qlib）")
    print("2. 完整回测（使用qlib，需要配置数据）")
    
    choice = input("请输入选择 (1/2): ").strip()
    
    if choice == "1":
        # 简单评估
        results = create_simple_factor_evaluator(MINED_FACTORS_FILE)
        return results
    
    elif choice == "2":
        # 完整回测
        try:
            backtester = FactorBacktester(MINED_FACTORS_FILE)
            
            # 指定要测试的因子（可以修改这里来测试特定因子）
            specified_factors = None  # None表示测试所有因子
            
            # 运行综合回测
            results = backtester.run_comprehensive_backtest(specified_factors)
            
            # 生成报告
            if results:
                backtester.generate_backtest_report(results)
            
            return results
            
        except Exception as e:
            print(f"Qlib回测失败: {e}")
            print("将使用简单评估方式")
            results = create_simple_factor_evaluator(MINED_FACTORS_FILE)
            return results
    
    else:
        print("无效选择，使用简单评估")
        results = create_simple_factor_evaluator(MINED_FACTORS_FILE)
        return results

if __name__ == "__main__":
    main()
