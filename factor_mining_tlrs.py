# coding: utf-8
"""
基于TLRS（轨迹级奖励塑形）的因子挖掘模块
参考论文：Learning from Expert Factors: Trajectory-level Reward Shaping for Formulaic Alpha Mining
"""

import pandas as pd
import numpy as np
import os
import warnings
import datetime
from typing import List, Dict, Tuple, Optional
import random
from collections import defaultdict
import pickle
import json
from tqdm.auto import tqdm
from sklearn.preprocessing import StandardScaler
from sklearn.model_selection import train_test_split
from sklearn.metrics import mean_squared_error, mean_absolute_error
from scipy.stats import pearsonr, spearmanr
import matplotlib.pyplot as plt
import seaborn as sns

warnings.filterwarnings('ignore')

# --- 配置参数 ---
FACTORS_FILE = 'stock_factors_cyb.csv'
MINED_FACTORS_FILE = 'mined_factors_cyb.csv'
EXPERT_FACTORS_FILE = 'expert_factors.json'
MODEL_SAVE_PATH = 'tlrs_model.pkl'

# TLRS参数
MAX_SEQUENCE_LENGTH = 20  # 最大序列长度
MIN_SEQUENCE_LENGTH = 3   # 最小序列长度
POPULATION_SIZE = 100     # 种群大小
GENERATIONS = 50          # 进化代数
MUTATION_RATE = 0.1       # 变异率
CROSSOVER_RATE = 0.7      # 交叉率
ELITE_RATIO = 0.1         # 精英保留比例

# 奖励参数
IC_WEIGHT = 0.6           # IC权重
RANK_IC_WEIGHT = 0.4      # Rank IC权重
COMPLEXITY_PENALTY = 0.01 # 复杂度惩罚
STABILITY_WEIGHT = 0.2    # 稳定性权重

def load_expert_factors(expert_file):
    """
    加载专家因子定义
    如果文件不存在，创建默认的专家因子
    """
    try:
        if os.path.exists(expert_file):
            with open(expert_file, 'r', encoding='utf-8') as f:
                expert_data = json.load(f)
            
            if 'expert_factors' in expert_data:
                expert_factors = expert_data['expert_factors']
                print(f"成功加载 {len(expert_factors)} 个专家因子")
                return expert_factors
            else:
                print("专家因子文件格式错误，将使用默认因子")
        else:
            print(f"专家因子文件 {expert_file} 不存在，将创建默认因子")
            
    except Exception as e:
        print(f"加载专家因子文件失败: {e}")
    
    # 创建默认专家因子
    print("使用内置的默认专家因子")
    default_factors = [
        {
            "name": "price_momentum_10",
            "formula": "close / delay(close, 10) - 1",
            "description": "10日价格动量",
            "category": "momentum",
            "complexity": 2,
            "effectiveness_score": 0.75
        },
        {
            "name": "volume_price_correlation",
            "formula": "correlation(close, volume, 20)",
            "description": "价格成交量相关性",
            "category": "volume_price",
            "complexity": 3,
            "effectiveness_score": 0.68
        },
        {
            "name": "volatility_ratio",
            "formula": "std(returns, 5) / std(returns, 20)",
            "description": "短长期波动率比值",
            "category": "volatility",
            "complexity": 3,
            "effectiveness_score": 0.72
        }
    ]
    
    return default_factors

class TLRSFactorMiner:
    """基于TLRS的因子挖掘器"""
    
    def __init__(self, factors_data: pd.DataFrame, expert_factors: Optional[List[str]] = None):
        """
        初始化因子挖掘器
        
        Args:
            factors_data: 因子数据DataFrame
            expert_factors: 专家因子列表
        """
        self.factors_data = factors_data.copy()
        
        # 加载专家因子
        if expert_factors is None:
            expert_factor_data = load_expert_factors(EXPERT_FACTORS_FILE)
            self.expert_factors = [factor['name'] for factor in expert_factor_data]
            self.expert_factor_formulas = {factor['name']: factor['formula'] for factor in expert_factor_data}
            print(f"从专家因子库加载了 {len(self.expert_factors)} 个专家因子")
        else:
            self.expert_factors = expert_factors
            self.expert_factor_formulas = {}
        
        # 数据预处理
        self._preprocess_data()
        
        # 初始化操作符和操作数
        self._init_operators_and_operands()
        
        # 专家轨迹存储
        self.expert_trajectories = []
        self._build_expert_trajectories()
        
        # 训练历史
        self.training_history = {
            'generation': [],
            'best_fitness': [],
            'avg_fitness': [],
            'best_ic': [],
            'best_rank_ic': []
        }
    
    def _preprocess_data(self):
        """数据预处理"""
        print("开始数据预处理...")
        
        # 移除非数值列
        numeric_cols = self.factors_data.select_dtypes(include=[np.number]).columns
        non_factor_cols = ['ts_code', 'trade_date', 'target_return_close', 'target_return_close_label', 'industry']
        self.factor_columns = [col for col in numeric_cols if col not in non_factor_cols]
        
        # 处理缺失值和异常值
        for col in self.factor_columns:
            # 填充缺失值
            self.factors_data[col] = self.factors_data[col].fillna(0)
            
            # 异常值处理（3倍标准差）
            mean_val = self.factors_data[col].mean()
            std_val = self.factors_data[col].std()
            lower_bound = mean_val - 3 * std_val
            upper_bound = mean_val + 3 * std_val
            self.factors_data[col] = self.factors_data[col].clip(lower_bound, upper_bound)
        
        # 标准化
        scaler = StandardScaler()
        self.factors_data[self.factor_columns] = scaler.fit_transform(
            self.factors_data[self.factor_columns]
        )
        
        print(f"预处理完成，共有 {len(self.factor_columns)} 个因子")
    
    def _init_operators_and_operands(self):
        """初始化操作符和操作数"""
        # 二元操作符
        self.binary_operators = [
            '+', '-', '*', '/', 'max', 'min', 'corr', 'cov'
        ]
        
        # 一元操作符
        self.unary_operators = [
            'abs', 'log', 'sqrt', 'square', 'rank', 'zscore', 
            'ts_mean', 'ts_std', 'ts_max', 'ts_min', 'ts_sum',
            'delta', 'delay'
        ]
        
        # 操作数（因子名称）
        self.operands = self.factor_columns.copy()
        
        # 常数
        self.constants = [0.1, 0.5, 1.0, 2.0, 5.0, 10.0, 20.0, 30.0, 60.0]
        
        # 所有token
        self.all_tokens = (
            self.binary_operators + 
            self.unary_operators + 
            self.operands + 
            [str(c) for c in self.constants]
        )
        
        print(f"初始化完成：{len(self.binary_operators)}个二元操作符，"
              f"{len(self.unary_operators)}个一元操作符，"
              f"{len(self.operands)}个操作数")
    
    def _build_expert_trajectories(self):
        """构建专家轨迹"""
        print("构建专家轨迹...")
        
        # 如果没有提供专家因子，使用一些经典的因子组合
        if not self.expert_factors:
            self.expert_factors = [
                # 动量因子
                "momentum_20",
                "cumret_20", 
                "rsi_20",
                
                # 反转因子
                "short_reversal_5",
                "short_reversal_10",
                
                # 波动率因子
                "volatility_20",
                "atr_20",
                
                # 价格因子
                "ma_bias_20",
                "bb_position_20",
                
                # 成交量因子
                "relative_volume_20",
                "price_volume_corr_20",
                
                # Alpha101因子
                "alpha006",
                "alpha012",
                "alpha016"
            ]
        
        # 为每个专家因子创建简单的轨迹
        for factor in self.expert_factors:
            if factor in self.factor_columns:
                # 简单轨迹：直接使用因子
                trajectory = [factor]
                self.expert_trajectories.append(trajectory)
                
                # 复合轨迹：因子的变换
                if len(factor) < 15:  # 避免名称过长
                    trajectory_abs = ['abs', factor]
                    trajectory_rank = ['rank', factor]
                    trajectory_zscore = ['zscore', factor]
                    
                    self.expert_trajectories.extend([
                        trajectory_abs, trajectory_rank, trajectory_zscore
                    ])
        
        print(f"构建了 {len(self.expert_trajectories)} 条专家轨迹")
    
    def _generate_random_sequence(self) -> List[str]:
        """生成随机序列"""
        length = random.randint(MIN_SEQUENCE_LENGTH, MAX_SEQUENCE_LENGTH)
        sequence = []
        
        for i in range(length):
            if i == 0 or random.random() < 0.6:
                # 选择操作数或常数
                if random.random() < 0.8:
                    token = random.choice(self.operands)
                else:
                    token = str(random.choice(self.constants))
            else:
                # 选择操作符
                if random.random() < 0.7:
                    token = random.choice(self.unary_operators)
                else:
                    token = random.choice(self.binary_operators)
            
            sequence.append(token)
        
        return sequence
    
    def _calculate_subsequence_similarity(self, sequence: List[str]) -> float:
        """计算子序列相似度（TLRS核心）"""
        if not sequence or not self.expert_trajectories:
            return 0.0
        
        max_similarity = 0.0
        
        for expert_traj in self.expert_trajectories:
            # 计算与专家轨迹的最大公共子序列长度
            similarity = self._lcs_similarity(sequence, expert_traj)
            max_similarity = max(max_similarity, similarity)
        
        return max_similarity
    
    def _lcs_similarity(self, seq1: List[str], seq2: List[str]) -> float:
        """计算最长公共子序列相似度"""
        if not seq1 or not seq2:
            return 0.0
        
        m, n = len(seq1), len(seq2)
        dp = [[0] * (n + 1) for _ in range(m + 1)]
        
        for i in range(1, m + 1):
            for j in range(1, n + 1):
                if seq1[i-1] == seq2[j-1]:
                    dp[i][j] = dp[i-1][j-1] + 1
                else:
                    dp[i][j] = max(dp[i-1][j], dp[i][j-1])
        
        lcs_length = dp[m][n]
        return lcs_length / max(m, n)
    
    def _evaluate_sequence(self, sequence: List[str]) -> Dict[str, float]:
        """评估序列性能"""
        try:
            # 计算因子值
            factor_values = self._compute_factor_values(sequence)
            
            if factor_values is None or len(factor_values) == 0:
                return {
                    'ic': 0.0,
                    'rank_ic': 0.0,
                    'fitness': 0.0,
                    'similarity': 0.0,
                    'complexity': len(sequence),
                    'stability': 0.0
                }
            
            # 计算IC和Rank IC  
            target = self.factors_data['target_return_close'].values
            valid_mask = ~(np.isnan(factor_values) | np.isnan(target))
            
            if valid_mask.sum() < 100:  # 需要足够的有效数据点
                return {
                    'ic': 0.0,
                    'rank_ic': 0.0,
                    'fitness': 0.0,
                    'similarity': 0.0,
                    'complexity': len(sequence),
                    'stability': 0.0
                }
            
            factor_valid = factor_values[valid_mask]
            target_valid = target[valid_mask]
            
            # IC (Pearson相关系数)
            ic, _ = pearsonr(factor_valid, target_valid)
            ic = abs(ic) if not np.isnan(ic) else 0.0
            
            # Rank IC (Spearman相关系数)
            rank_ic, _ = spearmanr(factor_valid, target_valid)
            rank_ic = abs(rank_ic) if not np.isnan(rank_ic) else 0.0
            
            # 计算稳定性（时间序列稳定性）
            stability = self._calculate_stability(factor_values, target)
            
            # 计算子序列相似度
            similarity = self._calculate_subsequence_similarity(sequence)
            
            # 复杂度惩罚
            complexity_penalty = COMPLEXITY_PENALTY * len(sequence)
            
            # 综合适应度
            fitness = (
                IC_WEIGHT * ic + 
                RANK_IC_WEIGHT * rank_ic + 
                STABILITY_WEIGHT * stability +
                0.1 * similarity -  # TLRS奖励
                complexity_penalty
            )
            
            return {
                'ic': ic,
                'rank_ic': rank_ic,
                'fitness': fitness,
                'similarity': similarity,
                'complexity': len(sequence),
                'stability': stability
            }
            
        except Exception as e:
            print(f"评估序列时出错: {e}")
            return {
                'ic': 0.0,
                'rank_ic': 0.0,
                'fitness': 0.0,
                'similarity': 0.0,
                'complexity': len(sequence),
                'stability': 0.0
            }
    
    def _compute_factor_values(self, sequence: List[str]) -> Optional[np.ndarray]:
        """计算因子值"""
        try:
            if not sequence:
                return None
            
            # 使用栈来计算表达式
            stack = []
            
            for token in sequence:
                if token in self.operands:
                    # 操作数（因子）
                    values = self.factors_data[token].values
                    stack.append(values)
                    
                elif token in [str(c) for c in self.constants]:
                    # 常数
                    const_val = float(token)
                    values = np.full(len(self.factors_data), const_val)
                    stack.append(values)
                    
                elif token in self.unary_operators:
                    # 一元操作符
                    if len(stack) < 1:
                        return None
                    
                    operand = stack.pop()
                    result = self._apply_unary_operator(token, operand)
                    if result is not None:
                        stack.append(result)
                    else:
                        return None
                        
                elif token in self.binary_operators:
                    # 二元操作符
                    if len(stack) < 2:
                        return None
                    
                    operand2 = stack.pop()
                    operand1 = stack.pop()
                    result = self._apply_binary_operator(token, operand1, operand2)
                    if result is not None:
                        stack.append(result)
                    else:
                        return None
            
            # 最终结果
            if len(stack) == 1:
                result = stack[0]
                # 处理无穷大和NaN
                result = np.where(np.isinf(result), 0, result)
                result = np.where(np.isnan(result), 0, result)
                return result
            else:
                return None
                
        except Exception as e:
            return None
    
    def _apply_unary_operator(self, op: str, operand: np.ndarray) -> Optional[np.ndarray]:
        """应用一元操作符"""
        try:
            if op == 'abs':
                return np.abs(operand)
            elif op == 'log':
                return np.log(np.abs(operand) + 1e-8)
            elif op == 'sqrt':
                return np.sqrt(np.abs(operand))
            elif op == 'square':
                return np.square(operand)
            elif op == 'rank':
                return pd.Series(operand).rank(pct=True).values
            elif op == 'zscore':
                mean_val = np.mean(operand)
                std_val = np.std(operand)
                if std_val == 0:
                    return np.zeros_like(operand)
                return (operand - mean_val) / std_val
            elif op == 'ts_mean':
                return pd.Series(operand).rolling(20, min_periods=1).mean().values
            elif op == 'ts_std':
                return pd.Series(operand).rolling(20, min_periods=1).std().values
            elif op == 'ts_max':
                return pd.Series(operand).rolling(20, min_periods=1).max().values
            elif op == 'ts_min':
                return pd.Series(operand).rolling(20, min_periods=1).min().values
            elif op == 'ts_sum':
                return pd.Series(operand).rolling(20, min_periods=1).sum().values
            elif op == 'delta':
                return pd.Series(operand).diff().fillna(0).values
            elif op == 'delay':
                return pd.Series(operand).shift(1).fillna(0).values
            else:
                return None
        except:
            return None
    
    def _apply_binary_operator(self, op: str, operand1: np.ndarray, operand2: np.ndarray) -> Optional[np.ndarray]:
        """应用二元操作符"""
        try:
            if op == '+':
                return operand1 + operand2
            elif op == '-':
                return operand1 - operand2
            elif op == '*':
                return operand1 * operand2
            elif op == '/':
                return np.divide(operand1, operand2 + 1e-8)
            elif op == 'max':
                return np.maximum(operand1, operand2)
            elif op == 'min':
                return np.minimum(operand1, operand2)
            elif op == 'corr':
                # 滚动相关系数
                df = pd.DataFrame({'x': operand1, 'y': operand2})
                return df['x'].rolling(20, min_periods=10).corr(df['y']).fillna(0).values
            elif op == 'cov':
                # 滚动协方差
                df = pd.DataFrame({'x': operand1, 'y': operand2})
                return df['x'].rolling(20, min_periods=10).cov(df['y']).fillna(0).values
            else:
                return None
        except:
            return None
    
    def _calculate_stability(self, factor_values: np.ndarray, target: np.ndarray) -> float:
        """计算因子稳定性"""
        try:
            # 将数据按时间分段，计算每段的IC
            n_segments = 5
            segment_size = len(factor_values) // n_segments
            
            ics = []
            for i in range(n_segments):
                start_idx = i * segment_size
                end_idx = (i + 1) * segment_size if i < n_segments - 1 else len(factor_values)
                
                segment_factor = factor_values[start_idx:end_idx]
                segment_target = target[start_idx:end_idx]
                
                valid_mask = ~(np.isnan(segment_factor) | np.isnan(segment_target))
                if valid_mask.sum() > 10:
                    ic, _ = pearsonr(segment_factor[valid_mask], segment_target[valid_mask])
                    if not np.isnan(ic):
                        ics.append(abs(ic))
            
            if len(ics) < 2:
                return 0.0
            
            # 稳定性 = 1 - IC的标准差
            stability = 1.0 - np.std(ics)
            return max(0.0, stability)
            
        except:
            return 0.0
    
    def _mutate_sequence(self, sequence: List[str]) -> List[str]:
        """变异序列"""
        mutated = sequence.copy()
        
        if random.random() < MUTATION_RATE:
            # 随机替换一个token
            if mutated:
                idx = random.randint(0, len(mutated) - 1)
                mutated[idx] = random.choice(self.all_tokens)
        
        if random.random() < MUTATION_RATE:
            # 随机添加一个token
            if len(mutated) < MAX_SEQUENCE_LENGTH:
                mutated.append(random.choice(self.all_tokens))
        
        if random.random() < MUTATION_RATE:
            # 随机删除一个token
            if len(mutated) > MIN_SEQUENCE_LENGTH:
                idx = random.randint(0, len(mutated) - 1)
                mutated.pop(idx)
        
        return mutated
    
    def _crossover_sequences(self, parent1: List[str], parent2: List[str]) -> Tuple[List[str], List[str]]:
        """交叉序列"""
        if random.random() > CROSSOVER_RATE:
            return parent1.copy(), parent2.copy()
        
        # 单点交叉
        min_len = min(len(parent1), len(parent2))
        if min_len <= 1:
            return parent1.copy(), parent2.copy()
        
        crossover_point = random.randint(1, min_len - 1)
        
        child1 = parent1[:crossover_point] + parent2[crossover_point:]
        child2 = parent2[:crossover_point] + parent1[crossover_point:]
        
        # 确保长度在合理范围内
        child1 = child1[:MAX_SEQUENCE_LENGTH]
        child2 = child2[:MAX_SEQUENCE_LENGTH]
        
        return child1, child2
    
    def mine_factors(self, n_factors: int = 10) -> List[Dict]:
        """挖掘因子"""
        print(f"开始挖掘 {n_factors} 个因子...")
        
        # 初始化种群
        population = []
        for _ in range(POPULATION_SIZE):
            sequence = self._generate_random_sequence()
            evaluation = self._evaluate_sequence(sequence)
            population.append({
                'sequence': sequence,
                'evaluation': evaluation
            })
        
        best_individuals = []
        
        # 进化过程
        for generation in range(GENERATIONS):
            print(f"第 {generation + 1}/{GENERATIONS} 代进化...")
            
            # 评估种群
            for individual in population:
                if 'evaluation' not in individual:
                    individual['evaluation'] = self._evaluate_sequence(individual['sequence'])
            
            # 按适应度排序
            population.sort(key=lambda x: x['evaluation']['fitness'], reverse=True)
            
            # 记录训练历史
            best_fitness = population[0]['evaluation']['fitness']
            avg_fitness = np.mean([ind['evaluation']['fitness'] for ind in population])
            best_ic = population[0]['evaluation']['ic']
            best_rank_ic = population[0]['evaluation']['rank_ic']
            
            self.training_history['generation'].append(generation)
            self.training_history['best_fitness'].append(best_fitness)
            self.training_history['avg_fitness'].append(avg_fitness)
            self.training_history['best_ic'].append(best_ic)
            self.training_history['best_rank_ic'].append(best_rank_ic)
            
            print(f"  最佳适应度: {best_fitness:.4f}, IC: {best_ic:.4f}, Rank IC: {best_rank_ic:.4f}")
            
            # 保留精英
            elite_size = int(POPULATION_SIZE * ELITE_RATIO)
            new_population = population[:elite_size]
            
            # 生成新个体
            while len(new_population) < POPULATION_SIZE:
                # 选择父代（轮盘赌选择）
                parent1 = self._tournament_selection(population)
                parent2 = self._tournament_selection(population)
                
                # 交叉
                child1_seq, child2_seq = self._crossover_sequences(
                    parent1['sequence'], parent2['sequence']
                )
                
                # 变异
                child1_seq = self._mutate_sequence(child1_seq)
                child2_seq = self._mutate_sequence(child2_seq)
                
                # 添加到新种群
                new_population.extend([
                    {'sequence': child1_seq},
                    {'sequence': child2_seq}
                ])
            
            population = new_population[:POPULATION_SIZE]
        
        # 最终评估并选择最佳因子
        for individual in population:
            individual['evaluation'] = self._evaluate_sequence(individual['sequence'])
        
        population.sort(key=lambda x: x['evaluation']['fitness'], reverse=True)
        
        # 选择前n_factors个因子
        best_individuals = population[:n_factors]
        
        print(f"因子挖掘完成！找到 {len(best_individuals)} 个优质因子")
        
        return best_individuals
    
    def _tournament_selection(self, population: List[Dict], tournament_size: int = 3) -> Dict:
        """锦标赛选择"""
        tournament = random.sample(population, min(tournament_size, len(population)))
        return max(tournament, key=lambda x: x['evaluation']['fitness'])
    
    def save_mined_factors(self, mined_factors: List[Dict], filename: str = None):
        """保存挖掘的因子"""
        if filename is None:
            filename = MINED_FACTORS_FILE
        
        # 计算因子值并保存
        factor_data = []
        
        for i, factor_info in enumerate(mined_factors):
            sequence = factor_info['sequence']
            evaluation = factor_info['evaluation']
            
            # 计算因子值
            factor_values = self._compute_factor_values(sequence)
            
            if factor_values is not None:
                factor_name = f"mined_factor_{i+1}"
                
                # 创建因子数据
                factor_df = self.factors_data[['ts_code', 'trade_date', 'target_return_close']].copy()
                factor_df[factor_name] = factor_values
                factor_df['factor_sequence'] = str(sequence)
                factor_df['factor_ic'] = evaluation['ic']
                factor_df['factor_rank_ic'] = evaluation['rank_ic']
                factor_df['factor_fitness'] = evaluation['fitness']
                
                factor_data.append(factor_df)
        
        if factor_data:
            # 合并所有因子数据
            combined_df = factor_data[0]
            for df in factor_data[1:]:
                factor_cols = [col for col in df.columns if col.startswith('mined_factor_')]
                combined_df = pd.merge(combined_df, df[['ts_code', 'trade_date'] + factor_cols], 
                                     on=['ts_code', 'trade_date'], how='left')
            
            # 保存到CSV
            combined_df.to_csv(filename, index=False, encoding='utf-8-sig')
            print(f"挖掘的因子已保存到: {filename}")
            
            # 保存因子信息到JSON
            factor_info_file = filename.replace('.csv', '_info.json')
            factor_info = []
            for i, factor in enumerate(mined_factors):
                factor_info.append({
                    'factor_name': f"mined_factor_{i+1}",
                    'sequence': factor['sequence'],
                    'evaluation': factor['evaluation']
                })
            
            with open(factor_info_file, 'w', encoding='utf-8') as f:
                json.dump(factor_info, f, ensure_ascii=False, indent=2)
            
            print(f"因子信息已保存到: {factor_info_file}")
        
        return filename
    
    def plot_training_history(self):
        """绘制训练历史"""
        fig, axes = plt.subplots(2, 2, figsize=(15, 10))
        
        # 适应度曲线
        axes[0, 0].plot(self.training_history['generation'], self.training_history['best_fitness'], 
                       label='Best Fitness', color='red')
        axes[0, 0].plot(self.training_history['generation'], self.training_history['avg_fitness'], 
                       label='Avg Fitness', color='blue')
        axes[0, 0].set_title('Fitness Evolution')
        axes[0, 0].set_xlabel('Generation')
        axes[0, 0].set_ylabel('Fitness')
        axes[0, 0].legend()
        axes[0, 0].grid(True)
        
        # IC曲线
        axes[0, 1].plot(self.training_history['generation'], self.training_history['best_ic'], 
                       label='Best IC', color='green')
        axes[0, 1].set_title('IC Evolution')
        axes[0, 1].set_xlabel('Generation')
        axes[0, 1].set_ylabel('IC')
        axes[0, 1].legend()
        axes[0, 1].grid(True)
        
        # Rank IC曲线
        axes[1, 0].plot(self.training_history['generation'], self.training_history['best_rank_ic'], 
                       label='Best Rank IC', color='orange')
        axes[1, 0].set_title('Rank IC Evolution')
        axes[1, 0].set_xlabel('Generation')
        axes[1, 0].set_ylabel('Rank IC')
        axes[1, 0].legend()
        axes[1, 0].grid(True)
        
        # 综合图
        axes[1, 1].plot(self.training_history['generation'], self.training_history['best_fitness'], 
                       label='Fitness', alpha=0.7)
        axes[1, 1].plot(self.training_history['generation'], self.training_history['best_ic'], 
                       label='IC', alpha=0.7)
        axes[1, 1].plot(self.training_history['generation'], self.training_history['best_rank_ic'], 
                       label='Rank IC', alpha=0.7)
        axes[1, 1].set_title('Overall Evolution')
        axes[1, 1].set_xlabel('Generation')
        axes[1, 1].set_ylabel('Value')
        axes[1, 1].legend()
        axes[1, 1].grid(True)
        
        plt.tight_layout()
        plt.savefig('tlrs_training_history.png', dpi=300, bbox_inches='tight')
        plt.show()
        
        print("训练历史图已保存为: tlrs_training_history.png")

def main():
    """主函数"""
    print("=== TLRS因子挖掘系统 ===")
    
    # 检查因子数据文件
    if not os.path.exists(FACTORS_FILE):
        print(f"错误：找不到因子数据文件 {FACTORS_FILE}")
        print("请先运行 calculate_stock_factors_trade.py 生成因子数据")
        return
    
    # 加载因子数据
    print("加载因子数据...")
    factors_data = pd.read_csv(FACTORS_FILE, encoding='utf-8')
    factors_data['trade_date'] = pd.to_datetime(factors_data['trade_date'])
    
    print(f"加载完成，数据量: {len(factors_data)} 行")
    
    # 初始化TLRS挖掘器
    miner = TLRSFactorMiner(factors_data)
    
    # 挖掘因子
    mined_factors = miner.mine_factors(n_factors=10)
    
    # 保存结果
    output_file = miner.save_mined_factors(mined_factors)
    
    # 绘制训练历史
    miner.plot_training_history()
    
    # 打印最佳因子信息
    print("\n=== 最佳挖掘因子 ===")
    for i, factor in enumerate(mined_factors[:5]):  # 显示前5个
        eval_info = factor['evaluation']
        print(f"\n因子 {i+1}:")
        print(f"  序列: {factor['sequence']}")
        print(f"  IC: {eval_info['ic']:.4f}")
        print(f"  Rank IC: {eval_info['rank_ic']:.4f}")
        print(f"  适应度: {eval_info['fitness']:.4f}")
        print(f"  稳定性: {eval_info['stability']:.4f}")
        print(f"  复杂度: {eval_info['complexity']}")
    
    print(f"\n因子挖掘完成！结果已保存到: {output_file}")

if __name__ == "__main__":
    main()
